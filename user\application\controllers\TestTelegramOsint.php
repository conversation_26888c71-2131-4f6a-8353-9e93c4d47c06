<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Test controller for Telegram OSINT queue functionality
 * Access via: /user/TestTelegramOsint/testQueue
 */
class TestTelegramOsint extends InSecureController
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('OsintModel');
        $this->load->model('TelegramModel');
    }
    
    /**
     * Test the OSINT queue functionality
     */
    public function testQueue()
    {
        echo "<h2>Telegram OSINT Queue Test</h2>";
        
        // Check if database fields exist
        echo "<h3>1. Database Schema Check</h3>";
        if ($this->db->field_exists('chat_id', 'tb_osint')) {
            echo "✅ chat_id field exists in tb_osint<br>";
        } else {
            echo "❌ chat_id field missing in tb_osint - using metadata fallback<br>";
        }
        
        if ($this->db->field_exists('telegram_id', 'tb_osint')) {
            echo "✅ telegram_id field exists in tb_osint<br>";
        } else {
            echo "❌ telegram_id field missing in tb_osint - using metadata fallback<br>";
        }
        
        // Check pending OSINT requests
        echo "<h3>2. Pending OSINT Requests</h3>";
        $pendingRequests = $this->OsintModel->find(['status' => 'pending'], '*');
        echo "Pending requests: " . count($pendingRequests) . "<br>";
        
        if (count($pendingRequests) > 0) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Username</th><th>UserType</th><th>Status</th><th>Created</th><th>Chat ID</th></tr>";
            foreach ($pendingRequests as $request) {
                $chatId = isset($request->chat_id) ? $request->chat_id : 'N/A';
                if ($chatId == 'N/A' && isset($request->metadata)) {
                    $metadata = json_decode($request->metadata, true);
                    $chatId = isset($metadata['chat_id']) ? $metadata['chat_id'] : 'N/A';
                }
                echo "<tr>";
                echo "<td>{$request->id}</td>";
                echo "<td>{$request->username}</td>";
                echo "<td>" . (isset($request->userType) ? $request->userType : 'N/A') . "</td>";
                echo "<td>{$request->status}</td>";
                echo "<td>" . (isset($request->createdOn) ? $request->createdOn : 'N/A') . "</td>";
                echo "<td>{$chatId}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Check recent OSINT requests
        echo "<h3>3. Recent OSINT Requests (Last 10)</h3>";
        $recentRequests = $this->OsintModel->find(['userType' => 'telegram'], '*', ['limit' => 10]);
        echo "Recent Telegram requests: " . count($recentRequests) . "<br>";
        
        if (count($recentRequests) > 0) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Username</th><th>Status</th><th>Created</th></tr>";
            foreach ($recentRequests as $request) {
                echo "<tr>";
                echo "<td>{$request->id}</td>";
                echo "<td>{$request->username}</td>";
                echo "<td>{$request->status}</td>";
                echo "<td>" . (isset($request->createdOn) ? $request->createdOn : 'N/A') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Test directory permissions
        echo "<h3>4. Directory Permissions Check</h3>";
        $uploadDir = FCPATH . "uploads/telegram_osint/";
        if (!is_dir($uploadDir)) {
            if (mkdir($uploadDir, 0755, true)) {
                echo "✅ Created uploads/telegram_osint/ directory<br>";
            } else {
                echo "❌ Failed to create uploads/telegram_osint/ directory<br>";
            }
        } else {
            echo "✅ uploads/telegram_osint/ directory exists<br>";
        }
        
        if (is_writable($uploadDir)) {
            echo "✅ uploads/telegram_osint/ directory is writable<br>";
        } else {
            echo "❌ uploads/telegram_osint/ directory is not writable<br>";
        }
        
        echo "<h3>5. Manual Cron Test</h3>";
        echo "<a href='/user/OsintCron/index' target='_blank'>Run OSINT Cron Manually</a><br>";
        echo "<small>This will process any pending OSINT requests</small><br>";
        
        echo "<h3>6. Test Instructions</h3>";
        echo "<ol>";
        echo "<li>Send an OSINT request via Telegram: <code>osint <EMAIL></code></li>";
        echo "<li>Check that you receive an immediate 'processing' message</li>";
        echo "<li>Refresh this page to see the request in the pending list</li>";
        echo "<li>Run the cron manually or wait for it to process</li>";
        echo "<li>Check that you receive the PDF via Telegram</li>";
        echo "</ol>";
    }
    
    /**
     * Create a test OSINT request
     */
    public function createTestRequest()
    {
        echo "<h2>Creating Test OSINT Request</h2>";
        
        // Create a test request
        $testData = [
            'userType' => 'telegram',
            'userId' => 1, // Use a valid user ID from your system
            'username' => '<EMAIL>',
            'status' => 'pending',
            'createdOn' => date('Y-m-d H:i:s')
        ];
        
        // Add Telegram-specific data
        if ($this->db->field_exists('chat_id', 'tb_osint')) {
            $testData['chat_id'] = '123456789'; // Test chat ID
            $testData['telegram_id'] = '987654321'; // Test telegram ID
        } else {
            $testData['metadata'] = json_encode([
                'chat_id' => '123456789',
                'telegram_id' => '987654321'
            ]);
        }
        
        $osintId = $this->OsintModel->attach($testData);
        
        if ($osintId) {
            echo "✅ Test OSINT request created with ID: {$osintId}<br>";
            echo "<a href='/user/TestTelegramOsint/testQueue'>View Test Results</a>";
        } else {
            echo "❌ Failed to create test OSINT request<br>";
        }
    }
}
