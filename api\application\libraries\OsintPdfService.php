<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Unified OSINT PDF Generation Service
 * 
 * This service provides consistent PDF generation for OSINT reports
 * across all channels (API, Telegram, Web interface)
 */
class OsintPdfService {

    protected $CI;

    public function __construct() {
        $this->CI =& get_instance();
        $this->CI->config->load('osint_api_mappings');
        $this->CI->load->model('OsintApplicationModel');
        $this->CI->load->model('OsintModel');
        $this->CI->load->library('pdf');
    }

    /**
     * Generate PDF from OSINT ID (for API/Web interface)
     * 
     * @param int $osintId OSINT record ID
     * @param string $generatedBy Who generated the report
     * @return array PDF data with file path and public URL
     */
    public function generateFromOsintId($osintId, $generatedBy = 'CCAS') {
        // Get OSINT data from database
        $osintDetails = $this->CI->OsintApplicationModel->find(['osintId' => $osintId]);
        $osint = $this->CI->OsintModel->findOne(['id' => $osintId]);
        
        if (!$osint) {
            throw new Exception("OSINT record not found");
        }

        // Process the data using the same logic as API
        $processedData = $this->processOsintData($osintDetails, $osint);
        
        // Generate PDF
        return $this->generatePdf($processedData, $osint->username, $generatedBy);
    }

    /**
     * Generate PDF from raw OSINT result data (for Telegram/direct API calls)
     * 
     * @param array $rawResult Raw OSINT API result
     * @param string $username Target username/email/phone
     * @param string $generatedBy Who generated the report
     * @return array PDF data with file path and public URL
     */
    public function generateFromRawData($rawResult, $username, $generatedBy = 'CCAS') {
        // Convert raw result to processed format
        $processedData = $this->processRawOsintData($rawResult, $username);
        
        // Generate PDF
        return $this->generatePdf($processedData, $username, $generatedBy);
    }

    /**
     * Process OSINT data from database records (API/Web format)
     */
    private function processOsintData($osintDetails, $osint) {
        $mappings = $this->CI->config->item('api_field_mappings');
        $nameList = [];
        $emailList = [];
        $companyNameList = [];
        $mobileList = [];
        $osintList = [];
        $locationList = [];
        $leakData = [];
        $companyInfoList = [];

        foreach($osintDetails as $detail) {
            if($detail->details && $detail->details !== 'null') {
                $content = json_decode($detail->details);
                
                // Process different API types
                if($detail->appName == 'LeakData API' && isset($content->List)) {
                    $this->processLeakData($content, $leakData, $nameList, $emailList);
                } elseif($detail->appName == 'Truecaller') {
                    $this->processTruecallerData($content, $osintList, $nameList, $emailList, $mobileList);
                } elseif($detail->appName == 'PaymentMethod') {
                    $this->processPaymentData($content, $osintList, $mobileList);
                } elseif($detail->appName == 'CompanySearch') {
                    $this->processCompanyData($content, $companyInfoList, $companyNameList);
                } else {
                    $this->processGenericData($detail, $content, $osintList, $nameList, $emailList, $locationList, $mappings);
                }
            }
        }

        return [
            'osint' => $osint,
            'osintList' => $osintList,
            'nameList' => array_keys($nameList),
            'emailList' => array_keys($emailList),
            'mobileList' => array_keys($mobileList),
            'companyNameList' => array_keys($companyNameList),
            'locationList' => array_keys($locationList),
            'leakData' => $leakData,
            'companyInfoList' => $companyInfoList,
            'reportCreated' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Process raw OSINT data (Telegram format)
     */
    private function processRawOsintData($rawResult, $username) {
        $nameList = [];
        $emailList = [];
        $mobileList = [];
        $osintList = [];
        $locationList = [];
        $leakData = [];
        $companyInfoList = [];

        // Process bulk data if present
        if (isset($rawResult['bulk']) && $rawResult['bulk']['status'] === 'success') {
            foreach ($rawResult['bulk']['data'] as $record) {
                $this->processRawRecord($record, $osintList, $nameList, $emailList, $mobileList, $locationList, $leakData);
            }
        }

        // Process WhatsApp data
        if (isset($rawResult['fetch_whatsapp']) && $rawResult['fetch_whatsapp']['status'] === 'success') {
            $this->processWhatsAppData($rawResult['fetch_whatsapp'], $osintList);
        }

        // Process Telegram data
        if (isset($rawResult['fetch_telegram']) && $rawResult['fetch_telegram']['status'] === 'success') {
            $this->processTelegramData($rawResult['fetch_telegram'], $osintList);
        }

        // Create mock osint object for compatibility
        $osint = (object)[
            'username' => $username,
            'id' => time() // Use timestamp as ID for raw data
        ];

        return [
            'osint' => $osint,
            'osintList' => $osintList,
            'nameList' => array_keys($nameList),
            'emailList' => array_keys($emailList),
            'mobileList' => array_keys($mobileList),
            'companyNameList' => [],
            'locationList' => array_keys($locationList),
            'leakData' => $leakData,
            'companyInfoList' => $companyInfoList,
            'reportCreated' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Generate the actual PDF file
     */
    private function generatePdf($data, $username, $generatedBy) {
        // Prepare view data
        $viewData = $data;
        $viewData['userName'] = (object)['displayName' => $generatedBy];
        
        // Generate HTML using the API template
        $html = $this->CI->load->view('pdf_template', $viewData, true);
        
        // Create unique filename
        $fileName = 'osint_report_' . time() . '_' . md5($username) . '.pdf';
        $filePath = FCPATH . 'uploads/telegram_osint/' . $fileName;
        $publicUrl = base_url('uploads/telegram_osint/' . $fileName);
        
        // Ensure directory exists
        $uploadDir = FCPATH . 'uploads/telegram_osint/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // Generate PDF
        $this->CI->pdf->telegramLink($html, $filePath);
        
        return [
            'file_path' => $filePath,
            'public_url' => $publicUrl,
            'file_name' => $fileName
        ];
    }

    // Helper methods for processing different data types
    private function processLeakData($content, &$leakData, &$nameList, &$emailList) {
        foreach ($content->List as $source => $sourceData) {
            $leakData[$source] = $sourceData;
            foreach ($sourceData->Data as $dataItem) {
                foreach ($dataItem as $k => $v) {
                    if (stripos($k, 'name') !== false && !empty($v)) {
                        $nameList[$v] = true;
                    }
                    if (stripos($k, 'email') !== false && !empty($v)) {
                        $emailList[$v] = true;
                    }
                }
            }
        }
    }

    private function processTruecallerData($content, &$osintList, &$nameList, &$emailList, &$mobileList) {
        if (is_array($content) && !empty($content[0])) {
            $tcData = $content[0];
            $osintList[] = [
                'api_name' => 'Truecaller',
                'name' => $tcData->name ? $tcData->name : 'N/A',
                'gender' => $tcData->gender ? $tcData->gender : 'N/A',
                'image' => $tcData->image ? $tcData->image : '',
                'phone' => $tcData->phones[0]->nationalFormat ? $tcData->phones[0]->nationalFormat : 'N/A',
                'email' => $tcData->internetAddresses[0]->id ? $tcData->internetAddresses[0]->id : 'N/A',
                'address' => $tcData->addresses[0]->address ? $tcData->addresses[0]->address : 'N/A',
                'operator' => $tcData->phones[0]->carrier ? $tcData->phones[0]->carrier : 'N/A'
            ];
            
            if (!empty($tcData->name)) $nameList[$tcData->name] = true;
            if (!empty($tcData->internetAddresses[0]->id)) $emailList[$tcData->internetAddresses[0]->id] = true;
            if (!empty($tcData->phones[0]->nationalFormat)) $mobileList[$tcData->phones[0]->nationalFormat] = true;
        }
    }

    private function processPaymentData($content, &$osintList, &$mobileList) {
        if (isset($content->PhonePe)) {
            $pp = $content->PhonePe;
            $osintList[] = [
                'api_name' => 'PhonePe',
                'account_exist' => $pp->is_registered ? $pp->is_registered : false,
                'name' => $pp->name ? $pp->name : '',
                'image' => $pp->profile_image->public_url ? $pp->profile_image->public_url : '',
                'success' => $pp->success ? $pp->success : false
            ];
            if (!empty($pp->mobile)) {
                $mobileList[$pp->mobile] = true;
            }
        }
        
        if (isset($content->GPay)) {
            $gp = $content->GPay;
            $osintList[] = [
                'api_name' => 'GPay',
                'account_exist' => $gp->is_registered ? $gp->is_registered : false,
                'name' => $gp->name ? $gp->name : '',
                'image' => $gp->profile_image->public_url ? $gp->profile_image->public_url : '',
                'success' => $gp->success ? $gp->success : false
            ];
        }
    }

    private function processCompanyData($content, &$companyInfoList, &$companyNameList) {
        if (isset($content->company_data) && isset($content->directors)) {
            $companyInfoList[] = [
                'company_data' => $content->company_data,
                'directors' => $content->directors
            ];
            $companyNameList[$content->company_data->company] = true;
        }
    }

    private function processGenericData($detail, $content, &$osintList, &$nameList, &$emailList, &$locationList, $mappings) {
        $appName = $detail->appName;

        if (isset($mappings[$appName])) {
            $mapping = $mappings[$appName];
            $extractedData = ['api_name' => $appName];

            foreach ($mapping as $field => $apiField) {
                if (!empty($apiField) && isset($content->$apiField)) {
                    $extractedData[$field] = $content->$apiField;

                    // Collect names and emails
                    if (stripos($field, 'name') !== false) {
                        $nameList[$content->$apiField] = true;
                    }
                    if (stripos($field, 'email') !== false) {
                        $emailList[$content->$apiField] = true;
                    }
                    if (stripos($field, 'address') !== false || stripos($field, 'location') !== false) {
                        $locationList[$content->$apiField] = true;
                    }
                }
            }

            if (count($extractedData) > 1) { // More than just api_name
                $osintList[] = $extractedData;
            }
        }
    }

    private function processRawRecord($record, &$osintList, &$nameList, &$emailList, &$mobileList, &$locationList, &$leakData) {
        $status = strtolower($record['Status']);

        // Skip non-existent records
        if (strpos($status, 'not exist') !== false || strpos($status, 'unauthorized') !== false) {
            return;
        }

        $apiName = $record['APIName'];

        // Handle LeakData API
        if ($apiName === 'LeakData API' && !empty($record['PayloadData'])) {
            $payload = json_decode($record['PayloadData'], true);
            if (isset($payload['List'])) {
                foreach ($payload['List'] as $source => $sourceData) {
                    $leakData[$source] = (object)$sourceData;
                    foreach ($sourceData['Data'] as $dataItem) {
                        foreach ($dataItem as $k => $v) {
                            if (stripos($k, 'name') !== false && !empty($v)) {
                                $nameList[$v] = true;
                            }
                            if (stripos($k, 'email') !== false && !empty($v)) {
                                $emailList[$v] = true;
                            }
                        }
                    }
                }
            }
        }

        // Handle Truecaller
        elseif ($apiName === 'Truecaller' && !empty($record['PayloadData'])) {
            $payload = json_decode($record['PayloadData'], true);
            if (is_array($payload) && !empty($payload[0])) {
                $tcData = $payload[0];
                $osintList[] = [
                    'api_name' => 'Truecaller',
                    'name' => $tcData['name'] ? $tcData['name'] : 'N/A',
                    'gender' => $tcData['gender'] ? $tcData['gender'] : 'N/A',
                    'image' => $tcData['image'] ? $tcData['image'] : '',
                    'phone' => $tcData['phones'][0]['nationalFormat'] ? $tcData['phones'][0]['nationalFormat'] : 'N/A',
                    'email' => $tcData['internetAddresses'][0]['id'] ? $tcData['internetAddresses'][0]['id'] : 'N/A',
                    'address' => $tcData['addresses'][0]['address'] ? $tcData['addresses'][0]['address'] : 'N/A',
                    'operator' => $tcData['phones'][0]['carrier'] ? $tcData['phones'][0]['carrier'] : 'N/A'
                ];

                if (!empty($tcData['name'])) $nameList[$tcData['name']] = true;
                if (!empty($tcData['internetAddresses'][0]['id'])) $emailList[$tcData['internetAddresses'][0]['id']] = true;
            }
        }

        // Handle other APIs
        else {
            $osintList[] = [
                'api_name' => $apiName,
                'account_exist' => 1,
                'status' => $record['Status']
            ];
        }
    }

    private function processWhatsAppData($whatsappData, &$osintList) {
        if (!empty($whatsappData['data'][0]['PayloadData'][0])) {
            $data = $whatsappData['data'][0]['PayloadData'][0];
            $osintList[] = [
                'api_name' => 'WhatsApp',
                'name' => $data['name'] ? $data['name'] : 'N/A',
                'phone' => $data['mobile'] ? $data['mobile'] : 'N/A',
                'bio' => $data['details'] ? $data['details'] : 'N/A',
                'account_exist' => 1
            ];
        }
    }

    private function processTelegramData($telegramData, &$osintList) {
        if (!empty($telegramData['data'][0]['PayloadData'][0])) {
            $data = $telegramData['data'][0]['PayloadData'][0];
            $osintList[] = [
                'api_name' => 'Telegram',
                'name' => $data['name'] ? $data['name'] : 'N/A',
                'phone' => $data['mobile'] ? $data['mobile'] : 'N/A',
                'account_exist' => 1
            ];
        }
    }
}
