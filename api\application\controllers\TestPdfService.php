<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require APPPATH . 'libraries/REST_Controller.php';

/**
 * Test Controller for API OSINT PDF Service
 * 
 * This controller verifies that the API can access the OsintPdfService library
 */
class TestPdfService extends REST_Controller {

    public function __construct() {
        parent::__construct();
        
        // Ensure this is only accessible in development/testing
        if (ENVIRONMENT === 'production') {
            show_404();
        }
    }

    /**
     * Test if the OsintPdfService library can be loaded
     */
    public function test_library_access_get() {
        try {
            // Try to load the library
            $this->load->library('OsintPdfService');
            
            $response = [
                'status' => 'success',
                'message' => 'OsintPdfService library loaded successfully',
                'library_class' => get_class($this->osintpdfservice),
                'methods' => get_class_methods($this->osintpdfservice),
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
            return $this->set_response($response, REST_Controller::HTTP_OK);
            
        } catch (Exception $e) {
            $response = [
                'status' => 'error',
                'message' => 'Failed to load OsintPdfService library',
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
            return $this->set_response($response, REST_Controller::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Test PDF template access
     */
    public function test_template_access_get() {
        try {
            // Test data for template
            $testData = [
                'osint' => (object)['username' => '<EMAIL>'],
                'osintList' => [
                    [
                        'api_name' => 'Test API',
                        'name' => 'Test User',
                        'account_exist' => true
                    ]
                ],
                'nameList' => ['Test User'],
                'emailList' => ['<EMAIL>'],
                'mobileList' => [],
                'companyNameList' => [],
                'locationList' => [],
                'leakData' => [],
                'companyInfoList' => [],
                'reportCreated' => date('Y-m-d H:i:s'),
                'userName' => (object)['displayName' => 'API Test']
            ];

            // Try to load the template
            $html = $this->load->view('pdf_template', $testData, true);
            
            $response = [
                'status' => 'success',
                'message' => 'PDF template loaded successfully',
                'html_length' => strlen($html),
                'template_path' => APPPATH . 'views/pdf_template.php',
                'template_exists' => file_exists(APPPATH . 'views/pdf_template.php'),
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
            return $this->set_response($response, REST_Controller::HTTP_OK);
            
        } catch (Exception $e) {
            $response = [
                'status' => 'error',
                'message' => 'Failed to load PDF template',
                'error' => $e->getMessage(),
                'template_path' => APPPATH . 'views/pdf_template.php',
                'template_exists' => file_exists(APPPATH . 'views/pdf_template.php'),
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
            return $this->set_response($response, REST_Controller::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Test system status
     */
    public function system_status_get() {
        $response = [
            'status' => 'info',
            'message' => 'API PDF Service System Status',
            'checks' => [
                'osint_pdf_service_library' => [
                    'path' => APPPATH . 'libraries/OsintPdfService.php',
                    'exists' => file_exists(APPPATH . 'libraries/OsintPdfService.php'),
                    'readable' => is_readable(APPPATH . 'libraries/OsintPdfService.php')
                ],
                'pdf_template' => [
                    'path' => APPPATH . 'views/pdf_template.php',
                    'exists' => file_exists(APPPATH . 'views/pdf_template.php'),
                    'readable' => is_readable(APPPATH . 'views/pdf_template.php')
                ],
                'upload_directory' => [
                    'path' => FCPATH . 'uploads/telegram_osint/',
                    'exists' => is_dir(FCPATH . 'uploads/telegram_osint/'),
                    'writable' => is_writable(FCPATH . 'uploads/telegram_osint/')
                ],
                'api_upload_directory' => [
                    'path' => FCPATH . 'uploads/osint/',
                    'exists' => is_dir(FCPATH . 'uploads/osint/'),
                    'writable' => is_writable(FCPATH . 'uploads/osint/')
                ]
            ],
            'environment' => ENVIRONMENT,
            'application_path' => APPPATH,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        return $this->set_response($response, REST_Controller::HTTP_OK);
    }

    /**
     * Test full PDF generation workflow (if OSINT data exists)
     */
    public function test_pdf_generation_get() {
        try {
            // Load required models and library
            $this->load->model('OsintModel');
            $this->load->library('OsintPdfService');
            
            // Find a recent OSINT record for testing
            $recentOsint = $this->OsintModel->findOne([], '*', ['orderBy' => ['createdOn', 'DESC']]);
            
            if (!$recentOsint) {
                $response = [
                    'status' => 'warning',
                    'message' => 'No OSINT records found for testing',
                    'timestamp' => date('Y-m-d H:i:s')
                ];
                return $this->set_response($response, REST_Controller::HTTP_OK);
            }

            // Test PDF generation
            $startTime = microtime(true);
            $pdfData = $this->osintpdfservice->generateFromOsintId($recentOsint->id, 'API Test');
            $endTime = microtime(true);
            
            $response = [
                'status' => 'success',
                'message' => 'PDF generated successfully',
                'test_osint_id' => $recentOsint->id,
                'test_username' => $recentOsint->username,
                'generation_time' => round(($endTime - $startTime), 3),
                'pdf_data' => $pdfData,
                'file_exists' => file_exists($pdfData['file_path']),
                'file_size_kb' => file_exists($pdfData['file_path']) ? round(filesize($pdfData['file_path']) / 1024, 2) : 0,
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
            return $this->set_response($response, REST_Controller::HTTP_OK);
            
        } catch (Exception $e) {
            $response = [
                'status' => 'error',
                'message' => 'PDF generation test failed',
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
            return $this->set_response($response, REST_Controller::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
