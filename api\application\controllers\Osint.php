<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Osint extends ApiController
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('OsintModel');
        $this->load->model("OsintApplicationModel");
        $this->load->model("userModel");
		$this->load->library('osintl');
    }

    function authToken($uname,$pwd) {
        $ch = curl_init();
        $param = array(
            'UserName' => $uname,
            'Password' => $pwd
        );
        $param = json_encode($param);
        curl_setopt($ch, CURLOPT_URL, 'https://mobileapi.lisw.in/api/Token');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $param);
        $headers = array();
        $headers[] = 'Content-Type: application/json';
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $result = curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Error:' . curl_error($ch);
        }
        curl_close($ch);
        $result = json_decode($result);
        $token = (array_key_exists('token',$result)) ? $result->token : '';

        return $token;
    }

    function create_post()
    {
        $this->requiredInputs = ['queryString','userId'];
        $this->optionalInputs = [];

        if (!$this->validatePostFields($this->requiredInputs, $this->optionalInputs)) {
            return $this->set_response($this->exitDanger($this->apiError), REST_Controller::HTTP_NOT_ACCEPTABLE);
        }
    
        $this->form_validation->set_rules('queryString', 'Email or Mobile Number', 'trim|required|xss_clean');

        if ($this->form_validation->run() === false) {
            return $this->set_response($this->exitDanger(validation_errors()), REST_Controller::HTTP_OK);
        } else {
            $userId = $this->input->post('userId');
            $username = $this->input->post('queryString');
            $userData = $this->userModel->findOne(['id' => $userId], "osintCredit,mobile");

            //check user credit
            if ($userData->osintCredit == 0) {
                return $this->set_response($this->exitDanger("Your Search Credit Over."), REST_Controller::HTTP_OK);
            }

            if(is_numeric($username)) {
                $this->form_validation->set_rules('mobile', 'mobile', 'trim|required|regex_match[/^[0-9]{10}$/]');
                $param['MobileNumber'] = $username;
				$param['EmailId'] = '';
            } else {
                $this->form_validation->set_rules('email', 'email', 'trim|required|valid_email');
                $param['EmailId'] = $username;
				$param['MobileNumber'] = '';
            }

            try {                
            
                $inputData['userId'] = $userId;
                $inputData['username'] = $username;				
                $osintId = $this->OsintModel->attach($inputData);

                if ($osintId):
                    $this->userModel->modify(["osintCredit" => $userData->osintCredit-1], ['id' => $userId]);               
                    return $this->set_response($this->exitSuccess("We have received your request. Please wait while we process your data."), REST_Controller::HTTP_OK);
                else:
                    return $this->set_response($this->exitDanger(), REST_Controller::HTTP_OK);
                endif;
                
            } catch (Exception $e) {
                return $this->set_response($e->getMessage(), REST_Controller::HTTP_OK);
            }
            
        }
    }

    function all_post()
    {
        $this->requiredInputs = ['userId', 'perPage', 'offset'];
        if (!$this->validatePostFields($this->requiredInputs, $this->optionalInputs)) {
            return $this->set_response($this->exitDanger($this->apiError), REST_Controller::HTTP_NOT_ACCEPTABLE);
        }
        $this->form_validation->set_rules('userId', 'user id', 'trim|required|xss_clean');
        $this->form_validation->set_rules('perPage', 'per page', 'trim|required|xss_clean');
        $this->form_validation->set_rules('offset', 'offset', 'trim|required|xss_clean');
        if ($this->form_validation->run() === false) {
            return $this->set_response($this->exitDanger(validation_errors()), REST_Controller::HTTP_OK);
        } else {
            $perPage = $this->input->post('perPage');
            $offset = $this->input->post('offset');
            $userId = $this->input->post('userId');
            $where = ['userId' => $userId];

            $osintList = $this->OsintModel->find($where, '*', ['orderBy' => ['createdOn', "DESC"],
                "limit" => ['perPage' => $perPage, 'offset' => $offset]]);
            $osintData = array();
			
            if (isArray($osintList)):
                foreach ($osintList as $v) {
                    $v->key = $v->status=='success' ? encrypt_url($v->id) : '';
                    $osintData[] = $v;
                }
            endif;
            return $this->set_response($this->exitSuccessWithMultiple($osintData), REST_Controller::HTTP_OK);
        }
    }
    
    function generatePdf_post()
    {
        $this->form_validation->set_rules('key', 'key', 'required');

        if ($this->form_validation->run() === false) {
            return $this->set_response($this->exitDanger(validation_errors()), REST_Controller::HTTP_OK);
        } else {
            $osintId = decrypt_url($this->input->post("key"));

            if ($osintId == '') {
                return $this->set_response($this->exitDanger("Please enter valid key"), REST_Controller::HTTP_OK);
            }

            try {
                // Load the unified PDF service
                $this->load->library('OsintPdfService');

                // Get user info for "Generated By" field
                $osint = $this->OsintModel->findOne(['t1.id' => $osintId]);
                if (!$osint) {
                    return $this->set_response($this->exitDanger("OSINT record not found"), REST_Controller::HTTP_OK);
                }

                // Get user details
                $this->load->model('userModel');
                $user = $this->userModel->findOne(['id' => $osint->userId]);
                $generatedBy = $user ? $user->displayName : 'API User';

                // Generate PDF using unified service
                $pdfData = $this->osintpdfservice->generateFromOsintId($osintId, $generatedBy);

                // Clean up old PDFs
                $this->load->helper('file');
                delete_files('uploads/osint/');

                // Move the generated PDF to the API uploads directory
                $apiPdfPath = 'uploads/osint/' . basename($pdfData['file_name']);
                $apiFullPath = FCPATH . $apiPdfPath;

                // Ensure API directory exists
                $apiUploadDir = FCPATH . 'uploads/osint/';
                if (!is_dir($apiUploadDir)) {
                    mkdir($apiUploadDir, 0755, true);
                }

                // Copy the PDF to API directory
                if (copy($pdfData['file_path'], $apiFullPath)) {
                    $pdfPath = base_url() . $apiPdfPath;
                    $osintData['pdf'] = $pdfPath;
                    return $this->set_response($this->exitSuccessWithOne($osintData), REST_Controller::HTTP_OK);
                } else {
                    return $this->set_response($this->exitDanger("Failed to generate PDF"), REST_Controller::HTTP_OK);
                }

            } catch (Exception $e) {
                log_message('error', "API PDF Generation Error: " . $e->getMessage());
                return $this->set_response($this->exitDanger("PDF generation failed: " . $e->getMessage()), REST_Controller::HTTP_OK);
            }
        }
    }



    function credit_post()
    {
        $this->requiredInputs = ['userId',];
        if (!$this->validatePostFields($this->requiredInputs, $this->optionalInputs)) {
            return $this->set_response($this->exitDanger($this->apiError), REST_Controller::HTTP_NOT_ACCEPTABLE);
        }
        $this->form_validation->set_rules('userId', 'userId', 'trim|required|xss_clean');
        if ($this->form_validation->run() === false) {
            return $this->set_response($this->exitDanger(validation_errors()), REST_Controller::HTTP_OK);
        } else {
            $userId = $this->input->post("userId");
            $this->load->model("userModel");
            $smsCredit = $this->userModel->findOne(['id' => $userId], "osintCredit");
            return $this->set_response($this->exitSuccessWithOne($smsCredit), REST_Controller::HTTP_OK);
        }
    }
    
}
