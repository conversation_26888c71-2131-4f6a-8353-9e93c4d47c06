-- SQL script to add Telegram-specific fields to tb_osint table
-- Run this script on your database to enable Telegram OSINT queue functionality

-- Add chat_id field for Telegram chat identification
ALTER TABLE tb_osint ADD COLUMN chat_id VARCHAR(50) NULL COMMENT 'Telegram chat ID for sending responses';

-- Add telegram_id field for Telegram user identification  
ALTER TABLE tb_osint ADD COLUMN telegram_id VARCHAR(50) NULL COMMENT 'Telegram user ID';

-- Add indexes for better performance
ALTER TABLE tb_osint ADD INDEX idx_telegram_chat (chat_id);
ALTER TABLE tb_osint ADD INDEX idx_telegram_user (telegram_id);

-- Verify the changes
DESCRIBE tb_osint;
