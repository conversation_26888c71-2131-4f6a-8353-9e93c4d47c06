<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSINT Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #f9f9f9;
        }
        header {
            background-color: #4682B4;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .container {
            width: 90%;
            margin: auto;
            padding: 20px;
            background: #fff;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            margin-top: 20px;
        }
        h2, h3 {
            color: #4682B4;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        table th, table td {
            border: 1px solid #ddd;
            padding: 10px;
        }
        table th {
            background-color: #4682B4;
            color: white;
        }
        .leaked-data-section {
            margin-top: 20px;
        }
        .leak-container {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f1f8ff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        footer {
            background-color: #4682B4;
            color: white;
            text-align: center;
            padding: 10px 0;
            margin-top: 20px;
        }
        .disclaimer {
            font-size: 0.9em;
            color: #555;
            margin-top: 20px;
        }
        .api-section {
            background-color: #f1f1f1;
            padding: 15px;
            margin: 20px 0;
            border-left: 6px solid #4682B4;
            border-radius: 8px;
        }
        .api-section.phonepe {
            background-color: #e7f3ff;
        }
        .api-section.gpay {
            background-color: #e6ffee;
        }
        .api-section.facebook {
            background-color: #e7f3ff;
        }
        .api-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 10px;
        }
        .api-header h3 {
            margin: 0;
            color: #2b4a76;
        }
        .api-header img {
            height: 36px;
        }
        ul {
            margin: 0;
            padding-left: 20px;
        }
        li {
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <header>
        <h1>LIS OSINT Report : <?php echo isset($osint->username) ? $osint->username : 'N/A'; ?></h1>
        <p>"Leaders in Cyber Crime Cases, Investigation, Seminars and Security Auditing !"</p>
    </header>
    <div class="container">
        <h2>Report Details</h2>
        <p><strong>Generated By:</strong> <?php echo isset($userName->displayName) ? $userName->displayName : 'CCAS'; ?></p>
        <p><strong>Date/Time:</strong> <?php echo $reportCreated; ?> IST</p>

        <h3>Alternate Names</h3>        
        <ul>
            <?php if (!empty($nameList)): ?>
                <?php foreach($nameList as $name): ?>
                    <li><?= htmlspecialchars($name) ?></li>
                <?php endforeach; ?>
            <?php else: ?>
                <li>No alternate names found</li>
            <?php endif; ?>
        </ul>

        <?php if (!empty($emailList)): ?>
            <h3>Email Address</h3>
            <ul>
                <?php foreach($emailList as $email): ?>
                    <li><?= htmlspecialchars($email) ?></li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>

        <?php if (!empty($mobileList)): ?>
            <div style="background: #fffbe6; border-radius: 8px; box-shadow: 0 2px 8px #f7e9b3; padding: 18px; margin-bottom: 24px;">
                <h3 style="margin-top: 0; color: #b8860b;">Mobile Numbers</h3>
                <ul style="margin: 0; padding-left: 20px;">
                    <?php foreach($mobileList as $mobile): ?>
                        <li><?= htmlspecialchars($mobile) ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if (!empty($companyNameList)): ?>
            <h3>Company Names</h3>
            <ul>
                <?php foreach($companyNameList as $cname): ?>
                    <li><?= htmlspecialchars($cname) ?></li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>

        <?php if (!empty($locationList)): ?>
            <h3>Locations</h3>            
            <ul>
                <?php foreach($locationList as $location): ?>
                    <li><?= htmlspecialchars($location) ?></li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>

        <?php if (!empty($osintList)): ?>
            <h2>Platform Information</h2>
            <?php foreach($osintList as $detail): ?>
                <?php if ($detail['api_name'] == 'Telegram') continue; ?>
                
                <?php
                $appName = $detail['api_name'];
                $bgClass = '';
                $icon = '';
                
                if ($appName === 'PhonePe') {
                    $bgClass = 'phonepe';
                    $icon = base_url('uploads/images/phone-pe.png');
                } elseif ($appName === 'GPay') {
                    $bgClass = 'gpay';
                    $icon = base_url('uploads/images/gpay.png');
                } elseif ($appName === 'Facebook') {
                    $bgClass = 'facebook';
                    $icon = base_url('uploads/images/fb.png');
                }
                ?>
                
                <div class="api-section <?= $bgClass ?>">
                    <div class="api-header">
                        <?php if ($icon && file_exists(FCPATH . 'uploads/images/' . strtolower($appName) . '.png')): ?>
                            <img src="<?= $icon ?>" alt="<?= $appName ?>">
                        <?php endif; ?>
                        <h3><?= htmlspecialchars($appName) ?></h3>
                    </div>
                    
                    <ul>
                        <?php if (!empty($detail['image'])): ?>
                            <?php
                            $image = $detail['image'];
                            if (preg_match('/^data:image\/[a-zA-Z]+;base64,/', $image)) {
                                $src = $image;
                            } elseif (preg_match('/^[A-Za-z0-9\/+\r\n]+={0,2}$/', $image) && base64_decode($image, true) !== false) {
                                $src = 'data:image/png;base64,' . $image;
                            } else {
                                $src = htmlspecialchars($image, ENT_QUOTES, 'UTF-8');
                            }
                            ?>
                            <li><strong>Profile Image:</strong><br><img src="<?= $src ?>" alt="Profile Image" style="max-width: 100px; margin-top: 5px; border: 1px solid #ccc;"></li>
                        <?php endif; ?>
                        
                        <?php if (isset($detail['account_exist'])): ?>
                            <li><strong>Status:</strong> Account Found</li>
                        <?php endif; ?>
                        
                        <?php
                        $fieldLabels = [
                            'id' => 'ID',
                            'uid' => 'UID',
                            'upi_id' => 'UPI ID',
                            'profile_id' => 'Profile ID',
                            'name' => 'Full Name',
                            'first_name' => 'First Name',
                            'last_name' => 'Last Name',
                            'full_name' => 'Full Name',
                            'banking_name' => 'Banking Name',
                            'gender' => 'Gender',
                            'email' => 'Email',
                            'email2' => 'Secondary Email',
                            'phone' => 'Phone',
                            'operator' => 'Operator',
                            'password' => 'Password',
                            'address' => 'Address',
                            'contact' => 'Contact Details',
                            'landmark' => 'Landmark',
                            'location' => 'Location',
                            'region' => 'Region',
                            'country' => 'Country',
                            'city' => 'City',
                            'state' => 'State',
                            'pin_code' => 'Pin Code',
                            'postal_code' => 'Postal Code',
                            'category' => 'Category',
                            'site' => 'Site',
                            'company_name' => 'Company Name',
                            'industry' => 'Industry',
                            'job_company' => 'Job Company',
                            'job_title' => 'Job Title',
                            'job_start_date' => 'Job Start Date',
                            'skills' => 'Skills',
                            'linkedin_id' => 'LinkedIn ID',
                            'ip' => 'IP Address',
                            'summary' => 'Summary',
                            'title' => 'Title',
                            'bio' => 'Bio',
                            'status' => 'Status',
                            'info' => 'Info',
                            'url' => 'URL'
                        ];
                        
                        foreach ($fieldLabels as $field => $label):
                            if (isset($detail[$field]) && !empty($detail[$field]) && $detail[$field] !== 'N/A'):
                        ?>
                            <li><strong><?= $label ?>:</strong> <?= htmlspecialchars($detail[$field]) ?></li>
                        <?php
                            endif;
                        endforeach;
                        ?>
                    </ul>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>

        <!-- Company Information Section -->
        <?php if (!empty($companyInfoList)): ?>
            <h2>Company Information</h2>
            <?php foreach($companyInfoList as $idx => $companyBlock): 
                $company = $companyBlock['company_data'];
                $directors = $companyBlock['directors'];
            ?>
            <div style="background: #eaf6ff; border-radius: 10px; box-shadow: 0 2px 8px #b3d1f3; padding: 24px; margin-bottom: 30px;">
                <h3 style="margin: 0; color: #2b4a76;">Company <?= $idx + 1 ?>: <?= htmlspecialchars($company->company) ?></h3>
                <table style="margin-top: 18px;">
                    <tr><th>CIN</th><td><?= htmlspecialchars($company->CIN) ?></td></tr>
                    <tr><th>Email</th><td><?= htmlspecialchars($company->emailAddress) ?></td></tr>
                    <tr><th>Date of Incorporation</th><td><?= htmlspecialchars($company->dateOfIncorporation) ?></td></tr>
                    <tr><th>Company Type</th><td><?= htmlspecialchars($company->companyType) ?></td></tr>
                    <tr><th>Category</th><td><?= htmlspecialchars($company->companyCategory) ?></td></tr>
                    <tr><th>Subcategory</th><td><?= htmlspecialchars($company->companySubcategory) ?></td></tr>
                    <tr><th>ROC Code</th><td><?= htmlspecialchars($company->ROCCode) ?></td></tr>
                    <tr><th>Authorised Capital</th><td><?= htmlspecialchars($company->authorisedCapital) ?></td></tr>
                    <tr><th>Paid Up Capital</th><td><?= htmlspecialchars($company->paidUpCapital) ?></td></tr>
                    <tr><th>Main Division</th><td><?= htmlspecialchars($company->mainDivisionDescription) ?></td></tr>
                    <tr><th>Status Under CIRP</th><td><?= htmlspecialchars($company->statusUnderCIRP) ?></td></tr>
                    <tr><th>Whether Listed</th><td><?= htmlspecialchars($company->whetherListedOrNot) ?></td></tr>
                    <tr><th>Date of Last AGM</th><td><?= htmlspecialchars($company->dateOfLastAGM) ?></td></tr>
                    <tr><th>Strike Off Date</th><td><?= htmlspecialchars($company->strikeOff_amalgamated_transferredDate) ?></td></tr>
                </table>
                <h4 style="margin-top: 28px; color: #4682B4;">Directors</h4>
                <table>
                    <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Mobile</th>
                        <th>DIN</th>
                        <th>Address</th>
                        <th>Country</th>
                        <th>State</th>
                        <th>City</th>
                    </tr>
                    <?php foreach($directors as $director): ?>
                    <tr>
                        <td>
                            <?= htmlspecialchars($director->FirstName) ?>
                            <?php if($director->MiddleName && $director->MiddleName !== 'null') echo ' ' . htmlspecialchars($director->MiddleName); ?>
                            <?= ' ' . htmlspecialchars($director->LastName) ?>
                        </td>
                        <td><?= htmlspecialchars($director->emailId) ?></td>
                        <td><?= htmlspecialchars($director->mobileNo) ?></td>
                        <td><?= htmlspecialchars($director->DIN) ?></td>
                        <td>
                            <?= htmlspecialchars($director->addressLine1) ?>
                            <?php if($director->addressLine2 && $director->addressLine2 !== 'null') echo ', ' . htmlspecialchars($director->addressLine2); ?>
                            <?php if($director->area && $director->area !== 'null') echo ', ' . htmlspecialchars($director->area); ?>
                            <?php if($director->pinCode && $director->pinCode !== 'null') echo ', ' . htmlspecialchars($director->pinCode); ?>
                        </td>
                        <td><?= htmlspecialchars($director->country) ?></td>
                        <td><?= htmlspecialchars($director->state) ?></td>
                        <td><?= htmlspecialchars($director->city) ?></td>
                    </tr>
                    <?php endforeach; ?>
                </table>
            </div>
            <?php endforeach; ?>
        <?php endif; ?>

        <!-- Leaked Data Section -->
        <h2>Leaked Data</h2>
        <div class="leaked-data-section">
            <?php if (empty($leakData)): ?>
                <h3>No leaked data found.</h3>
            <?php else: ?>
                <?php foreach($leakData as $apiName => $data): ?>
                <div class="leak-container">
                    <h3><?= htmlspecialchars($apiName) ?></h3>
                    <ul>                
                        <?php foreach($data->Data as $key => $userList): ?>
                            <?php foreach($userList as $label => $user): ?>
                                <li><?= htmlspecialchars($label) ?> : <?= htmlspecialchars($user) ?></li>
                            <?php endforeach; ?>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <h3>Disclaimer</h3>
        <p class="disclaimer">
            The content provided in this OSINT-generated report is specifically designed to assist law enforcement 
            agencies in their investigative efforts. This report is not admissible as legal evidence in court and 
            should not be treated as definitive proof. Its primary purpose is to support investigations 
            by offering actionable insights derived from open-source intelligence.
        </p>
    </div>
    <footer>
        <p>For any queries or feedback, please reach out to +91-9461101915</p>
    </footer>
</body>
</html>
