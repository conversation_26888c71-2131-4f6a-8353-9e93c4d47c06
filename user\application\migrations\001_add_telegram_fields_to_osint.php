<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Add_telegram_fields_to_osint extends CI_Migration {

    public function up()
    {
        // Add chat_id field for Telegram chat identification
        $fields = array(
            'chat_id' => array(
                'type' => 'VARCHAR',
                'constraint' => '50',
                'null' => TRUE,
                'comment' => 'Telegram chat ID for sending responses'
            ),
            'telegram_id' => array(
                'type' => 'VARCHAR',
                'constraint' => '50',
                'null' => TRUE,
                'comment' => 'Telegram user ID'
            )
        );
        
        $this->dbforge->add_column('tb_osint', $fields);
        
        // Add index for better performance
        $this->db->query('ALTER TABLE tb_osint ADD INDEX idx_telegram_chat (chat_id)');
        $this->db->query('ALTER TABLE tb_osint ADD INDEX idx_telegram_user (telegram_id)');
    }

    public function down()
    {
        // Remove the added fields
        $this->dbforge->drop_column('tb_osint', 'chat_id');
        $this->dbforge->drop_column('tb_osint', 'telegram_id');
    }
}
