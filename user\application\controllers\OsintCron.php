<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class OsintCron extends InSecureController
{

    function __construct()
    {
        parent::__construct();
        $this->load->model('OsintModel');
		$this->load->library('OsintServices');
        $this->load->model("userModel");
		$this->load->model("OsintApplicationModel");
    }
    
    function index(){
		
        $osint = $this->OsintModel->find(['status' => 'pending'], '*', ['limit'=>10]);
        //$osint = $this->OsintModel->find(['id'=>14],'*',['limit'=>10]);

        try {

            foreach ($osint as $data) {

                $this->OsintModel->modify(["status" => 'processing'], ['id' => $data->id]);

                $user_id = $data->userId;
                $username = $data->username;

                $user = $this->userModel->findOne(['id' => $user_id,], 'mobile, deviceId, password');

                $params = [
                    'userId' => $user_id,
                    'email' => '',
                    'mobile' => '',
                    'uname' => '9610505499',
                    'password' => '123456',
                    /* 'uname' => $user->mobile,
                    'password' => $user->password, */
                ];

                if (filter_var($username, FILTER_VALIDATE_EMAIL)) {
                    $params['email'] = $username;
                } else {
                    $params['mobile'] = $username;
                }          

                // Fetch Data
                try {
                    $result = $this->osintservices->fetchData($params);
                    $osintDetails = [];
                    $errorList = [];

                    foreach($result as $service_name => $osintData) {

                        $service_name = $service_name;

                        if($osintData['status'] == 'error') {
                            $errorList[] = $osintData['message'];
                            log_message('error', "Error in Osint service {$service_name} {$data->id} : {$username}");        
                            continue;
                        }

                        $osintDetails = $osintData['data'];

                        foreach($osintDetails as $details) {
                            if( strpos($details['Status'], 'is exist') === false )
                                continue;

                            $insertData = [];
                            
                            if (is_null($details['PayloadData']) || $details['PayloadData'] === '') {
                                $payLoadData = null;
                            } elseif(is_array($details['PayloadData'])) {
                                $payLoadData = json_encode($details['PayloadData']);
                            }
                            else {
                                $payLoadData = $details['PayloadData'];
                            }

                            if (is_null($details['Data']) || $details['Data'] === '') {
                                $otherData = null;
                            } elseif(is_array($details['Data'])) {
                                $otherData = json_encode($details['Data']);
                            }
                            else {
                                $otherData = $details['Data'];
                            }

                            $insertData['osintId'] = $data->id;
                            $insertData['appName'] = $details['APIName'];
                            $insertData['details'] = $otherData;
                            $insertData['content'] = $payLoadData;
                            $insertData['isExist'] = 1;

                            $this->OsintApplicationModel->attach($insertData);
                        }
                    }    

                    if(count($osintDetails) > 0) {
                        $this->OsintModel->modify(["status" => 'success'], ['id' => $data->id]);
                        log_message('info', "Data Processed for Osint Id {$data->id} : {$username}");

                        // Handle Telegram-specific processing
                        if(isset($data->userType) && $data->userType == 'telegram') {
                            $this->processTelegramOsintResult($data, $result, $username);
                        } else {
                            // Send regular notification for non-Telegram users
                            if($user->deviceId != '') {
                                $this->load->library('googlefcm');
                                $msg = "Your data has been processed.";
                                $title = 'OSINT';
                                $this->googlefcm->send($user->deviceId, $title, $msg, $title);
                            }
                        }
                    } else {
                        $errorList = json_encode($errorList);
                        $this->OsintModel->modify(["status" => 'failed', 'error_log' => $errorList], ['id' => $data->id]);
                        log_message('error', "Data Failed for Osint Id {$data->id} : {$username}");

                        // Send failure notification to Telegram if it's a Telegram request
                        if(isset($data->userType) && $data->userType == 'telegram' && isset($data->chat_id)) {
                            $this->sendTelegramFailureNotification($data->chat_id, $username);
                        }
                    }

                } catch(Exception $e) {
                    $this->OsintModel->modify(["status" => 'failed'], ['id' => $data->id]);
                    log_message('error', "Data Failed for Osint Id {$data->id} : {$username}");
                }

            }

        } catch (Exception $e) {            
            log_message('error', "Osint Module error from Cron: {$e->getMessage()}");
        }
    }
	
	/**
     * Process OSINT result for Telegram users
     */
    private function processTelegramOsintResult($data, $result, $username) {
        try {
            // Load required libraries
            $this->load->library('TelegramBot');
            $this->load->library('pdf');
            $this->load->helper('telegram');

            // Get chat_id from either direct field or metadata
            $chat_id = null;
            if (isset($data->chat_id)) {
                $chat_id = $data->chat_id;
            } elseif (isset($data->metadata)) {
                $metadata = json_decode($data->metadata, true);
                $chat_id = isset($metadata['chat_id']) ? $metadata['chat_id'] : null;
            }

            if (!$chat_id) {
                log_message('error', "No chat_id found for Telegram OSINT request - ID: {$data->id}");
                return;
            }

            // Generate PDF report
            $html = generateOsintPdfHtml($result, $username);

            $fileName = "osint_report_" . time() . "_" . $data->id . ".pdf";
            $filePath = FCPATH . "uploads/telegram_osint/" . $fileName;
            $publicUrl = base_url("uploads/telegram_osint/" . $fileName);

            // Ensure directory exists
            $uploadDir = FCPATH . "uploads/telegram_osint/";
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // Generate PDF
            $this->pdf->telegramLink($html, $filePath);

            // Send PDF to Telegram user
            if (file_exists($filePath)) {
                $msg = "✅ *Your OSINT Report is Ready!*\n\n";
                $msg .= "📋 **Target:** `$username`\n";
                $msg .= "📄 **Report:** [Download PDF]($publicUrl)\n\n";
                $msg .= "🎉 Processing completed successfully!";

                $this->telegrambot->sendMessage($chat_id, $msg, 'Markdown');

                log_message('info', "Telegram OSINT PDF sent successfully - ID: {$data->id}, Target: $username");
            } else {
                log_message('error', "Failed to generate PDF file - ID: {$data->id}");
                $this->sendTelegramFailureNotification($chat_id, $username);
            }

        } catch (Exception $e) {
            log_message('error', "Error processing Telegram OSINT result - ID: {$data->id}, Error: " . $e->getMessage());
            $chat_id = isset($data->chat_id) ? $data->chat_id : null;
            if (!$chat_id && isset($data->metadata)) {
                $metadata = json_decode($data->metadata, true);
                $chat_id = isset($metadata['chat_id']) ? $metadata['chat_id'] : null;
            }
            if ($chat_id) {
                $this->sendTelegramFailureNotification($chat_id, $username);
            }
        }
    }

    /**
     * Send failure notification to Telegram user
     */
    private function sendTelegramFailureNotification($chat_id, $username) {
        try {
            if (!isset($this->telegrambot)) {
                $this->load->library('TelegramBot');
            }

            $msg = "❌ *OSINT Processing Failed*\n\n";
            $msg .= "📋 **Target:** `$username`\n";
            $msg .= "⚠️ **Status:** Processing failed\n\n";
            $msg .= "Please try again later or contact support if the issue persists.";

            $this->telegrambot->sendMessage($chat_id, $msg, 'Markdown');

        } catch (Exception $e) {
            log_message('error', "Failed to send Telegram failure notification: " . $e->getMessage());
        }
    }
    
}