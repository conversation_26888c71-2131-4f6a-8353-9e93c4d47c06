# Telegram OSINT Queue Solution

## Problem
Your Telegram bot's OSINT service (`fetchOsintData`) takes 5-6 minutes to complete, but Telegram has a ~60 second timeout for webhook responses. This causes Telegram to retry the request repeatedly, leading to duplicate processing and poor user experience.

## Solution Overview
I've implemented a **queue-based asynchronous processing system** that:

1. **Immediately responds** to Telegram with a "processing" message (< 1 second)
2. **Queues the OSINT request** in the database with 'pending' status
3. **Processes requests in the background** using your existing cron system
4. **Sends the PDF result** back to Telegram when processing is complete

## Implementation Details

### 1. Modified `fetchOsintData` Method
**File:** `user/application/libraries/TelegramService.php`

The method now:
- Validates input and checks user credits
- Creates a pending OSINT record in the database
- Immediately responds to Telegram with processing status
- Deducts credits to prevent duplicate requests
- Returns within seconds (no timeout issues)

### 2. Enhanced `OsintCron.php`
**File:** `user/application/controllers/OsintCron.php`

The cron job now:
- Detects Telegram requests (`userType = 'telegram'`)
- Processes them using existing OSINT services
- Generates PDF reports for Telegram users
- Sends results back via Telegram bot
- Handles failures with appropriate notifications

### 3. Database Schema Update
**File:** `telegram_osint_database_update.sql`

Adds two new fields to `tb_osint` table:
- `chat_id`: Telegram chat ID for sending responses
- `telegram_id`: Telegram user ID for tracking

## Installation Instructions

### Step 1: Update Database Schema
Run the SQL script to add required fields:

```sql
-- Add Telegram-specific fields to tb_osint table
ALTER TABLE tb_osint ADD COLUMN chat_id VARCHAR(50) NULL COMMENT 'Telegram chat ID for sending responses';
ALTER TABLE tb_osint ADD COLUMN telegram_id VARCHAR(50) NULL COMMENT 'Telegram user ID';

-- Add indexes for better performance
ALTER TABLE tb_osint ADD INDEX idx_telegram_chat (chat_id);
ALTER TABLE tb_osint ADD INDEX idx_telegram_user (telegram_id);
```

### Step 2: Verify Cron Job Setup
Ensure your existing OSINT cron job is running:
```bash
# Add this to your crontab to run every minute
* * * * * /usr/bin/php /path/to/your/project/user/index.php OsintCron index
```

### Step 3: Test the Implementation
1. Send an OSINT request via Telegram
2. You should receive an immediate "processing" message
3. Wait 5-6 minutes for the background processing
4. You should receive the PDF report automatically

## How It Works

### User Experience Flow:
1. **User sends:** `osint <EMAIL>`
2. **Bot responds immediately:** "🔍 OSINT Request Received... Processing... This may take 5-6 minutes..."
3. **User can continue** using other bot services
4. **5-6 minutes later:** Bot sends the PDF report automatically

### Technical Flow:
1. `fetchOsintData()` creates pending record in `tb_osint`
2. Cron job (`OsintCron.php`) picks up pending requests
3. Processes using existing `osintservices->fetchData()`
4. Generates PDF using existing PDF library
5. Sends result back to Telegram using stored `chat_id`

## Benefits

✅ **No more timeouts** - Immediate response to Telegram
✅ **No duplicate requests** - Credits deducted immediately
✅ **Better user experience** - Clear status updates
✅ **Scalable** - Can handle multiple concurrent requests
✅ **Reuses existing infrastructure** - Minimal code changes
✅ **Fallback support** - Works with or without database changes

## Monitoring & Troubleshooting

### Check Logs
Monitor these log files for issues:
- `application/logs/` - General application logs
- Look for entries with "OSINT request queued" and "Telegram OSINT PDF sent"

### Common Issues
1. **PDF not generated**: Check if `uploads/telegram_osint/` directory exists and is writable
2. **No response from bot**: Verify cron job is running and processing requests
3. **Database errors**: Ensure the new fields were added correctly

### Manual Testing
You can manually trigger the cron job:
```bash
php /path/to/your/project/user/index.php OsintCron index
```

## Files Modified/Created

### Modified Files:
- `user/application/libraries/TelegramService.php` - Queue implementation
- `user/application/controllers/OsintCron.php` - Telegram processing

### New Files:
- `telegram_osint_database_update.sql` - Database schema update
- `user/application/migrations/001_add_telegram_fields_to_osint.php` - Migration file
- `TELEGRAM_OSINT_QUEUE_SOLUTION.md` - This documentation

## Next Steps

1. **Run the database update script**
2. **Test with a sample OSINT request**
3. **Monitor logs for any issues**
4. **Adjust cron frequency if needed** (currently every minute)

The solution maintains backward compatibility and will work even if the database fields aren't added (using a metadata fallback approach).
