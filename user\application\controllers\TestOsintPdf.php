<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Test Controller for OSINT PDF Unification
 * 
 * This controller provides testing utilities to verify that the unified
 * PDF generation service works correctly across all channels.
 */
class TestOsintPdf extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model('OsintModel');
        $this->load->model('OsintApplicationModel');
        $this->load->library('OsintPdfService');
        
        // Ensure this is only accessible in development/testing
        if (ENVIRONMENT === 'production') {
            show_404();
        }
    }

    /**
     * Test unified PDF service with database OSINT record
     */
    public function test_database_pdf($osintId = null) {
        if (!$osintId) {
            echo "<h2>Test Database PDF Generation</h2>";
            echo "<p>Usage: /test_osint_pdf/test_database_pdf/{osint_id}</p>";
            
            // Show available OSINT records
            $recent = $this->OsintModel->find([], '*', ['orderBy' => ['createdOn', 'DESC'], 'limit' => ['perPage' => 10, 'offset' => 0]]);
            echo "<h3>Recent OSINT Records:</h3><ul>";
            foreach ($recent as $record) {
                echo "<li><a href='" . base_url("test_osint_pdf/test_database_pdf/{$record->id}") . "'>";
                echo "ID: {$record->id} - {$record->username} - {$record->status}</a></li>";
            }
            echo "</ul>";
            return;
        }

        try {
            echo "<h2>Testing Database PDF Generation</h2>";
            echo "<p><strong>OSINT ID:</strong> $osintId</p>";
            
            $startTime = microtime(true);
            $pdfData = $this->osintpdfservice->generateFromOsintId($osintId, 'Test User');
            $endTime = microtime(true);
            
            echo "<p><strong>✅ Success!</strong></p>";
            echo "<p><strong>Generation Time:</strong> " . round(($endTime - $startTime), 2) . " seconds</p>";
            echo "<p><strong>File Path:</strong> {$pdfData['file_path']}</p>";
            echo "<p><strong>Public URL:</strong> <a href='{$pdfData['public_url']}' target='_blank'>{$pdfData['public_url']}</a></p>";
            echo "<p><strong>File Name:</strong> {$pdfData['file_name']}</p>";
            echo "<p><strong>File Exists:</strong> " . (file_exists($pdfData['file_path']) ? 'Yes' : 'No') . "</p>";
            
            if (file_exists($pdfData['file_path'])) {
                echo "<p><strong>File Size:</strong> " . round(filesize($pdfData['file_path']) / 1024, 2) . " KB</p>";
            }
            
        } catch (Exception $e) {
            echo "<p><strong>❌ Error:</strong> " . $e->getMessage() . "</p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }
    }

    /**
     * Test unified PDF service with raw data (Telegram format)
     */
    public function test_raw_data_pdf() {
        echo "<h2>Testing Raw Data PDF Generation</h2>";
        
        // Sample raw data structure (similar to what Telegram receives)
        $sampleRawData = [
            'bulk' => [
                'status' => 'success',
                'data' => [
                    [
                        'APIName' => 'Truecaller',
                        'Status' => 'Account Exist',
                        'PayloadData' => json_encode([
                            [
                                'name' => 'John Doe',
                                'gender' => 'Male',
                                'phones' => [
                                    ['nationalFormat' => '+91-**********', 'carrier' => 'Airtel']
                                ],
                                'internetAddresses' => [
                                    ['id' => '<EMAIL>']
                                ],
                                'addresses' => [
                                    ['address' => 'Mumbai, Maharashtra, India']
                                ]
                            ]
                        ])
                    ],
                    [
                        'APIName' => 'LeakData API',
                        'Status' => 'Data Found',
                        'PayloadData' => json_encode([
                            'List' => [
                                'DataBreach1' => [
                                    'Data' => [
                                        [
                                            'email' => '<EMAIL>',
                                            'name' => 'John Doe',
                                            'password' => 'hashed_password'
                                        ]
                                    ]
                                ]
                            ]
                        ])
                    ]
                ]
            ],
            'fetch_whatsapp' => [
                'status' => 'success',
                'data' => [
                    [
                        'PayloadData' => [
                            [
                                'name' => 'John Doe',
                                'mobile' => '+91**********',
                                'details' => 'Software Engineer'
                            ]
                        ]
                    ]
                ]
            ]
        ];

        try {
            $startTime = microtime(true);
            $pdfData = $this->osintpdfservice->generateFromRawData($sampleRawData, '<EMAIL>', 'Test Bot');
            $endTime = microtime(true);
            
            echo "<p><strong>✅ Success!</strong></p>";
            echo "<p><strong>Generation Time:</strong> " . round(($endTime - $startTime), 2) . " seconds</p>";
            echo "<p><strong>File Path:</strong> {$pdfData['file_path']}</p>";
            echo "<p><strong>Public URL:</strong> <a href='{$pdfData['public_url']}' target='_blank'>{$pdfData['public_url']}</a></p>";
            echo "<p><strong>File Name:</strong> {$pdfData['file_name']}</p>";
            echo "<p><strong>File Exists:</strong> " . (file_exists($pdfData['file_path']) ? 'Yes' : 'No') . "</p>";
            
            if (file_exists($pdfData['file_path'])) {
                echo "<p><strong>File Size:</strong> " . round(filesize($pdfData['file_path']) / 1024, 2) . " KB</p>";
            }
            
        } catch (Exception $e) {
            echo "<p><strong>❌ Error:</strong> " . $e->getMessage() . "</p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }
    }

    /**
     * Compare old vs new PDF generation
     */
    public function compare_implementations($osintId = null) {
        if (!$osintId) {
            echo "<h2>Compare PDF Implementations</h2>";
            echo "<p>Usage: /test_osint_pdf/compare_implementations/{osint_id}</p>";
            return;
        }

        echo "<h2>Comparing PDF Implementations</h2>";
        echo "<p><strong>OSINT ID:</strong> $osintId</p>";

        try {
            // Test new unified service
            echo "<h3>🆕 New Unified Service</h3>";
            $startTime = microtime(true);
            $newPdfData = $this->osintpdfservice->generateFromOsintId($osintId, 'Test User');
            $newTime = microtime(true) - $startTime;
            
            echo "<p>✅ Generated in " . round($newTime, 2) . " seconds</p>";
            echo "<p>📄 <a href='{$newPdfData['public_url']}' target='_blank'>View New PDF</a></p>";
            echo "<p>📊 Size: " . round(filesize($newPdfData['file_path']) / 1024, 2) . " KB</p>";

            // Summary
            echo "<h3>📊 Comparison Summary</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Metric</th><th>New Unified Service</th></tr>";
            echo "<tr><td>Generation Time</td><td>" . round($newTime, 2) . " seconds</td></tr>";
            echo "<tr><td>File Size</td><td>" . round(filesize($newPdfData['file_path']) / 1024, 2) . " KB</td></tr>";
            echo "<tr><td>Template</td><td>pdf_template.php (unified)</td></tr>";
            echo "<tr><td>Data Processing</td><td>OsintPdfService (unified)</td></tr>";
            echo "</table>";

        } catch (Exception $e) {
            echo "<p><strong>❌ Error:</strong> " . $e->getMessage() . "</p>";
        }
    }

    /**
     * Test template rendering
     */
    public function test_template() {
        echo "<h2>Testing PDF Template</h2>";
        
        // Sample data for template testing
        $testData = [
            'osint' => (object)['username' => '<EMAIL>'],
            'osintList' => [
                [
                    'api_name' => 'Truecaller',
                    'name' => 'John Doe',
                    'phone' => '+91-**********',
                    'email' => '<EMAIL>',
                    'address' => 'Mumbai, India'
                ],
                [
                    'api_name' => 'WhatsApp',
                    'name' => 'John Doe',
                    'account_exist' => true
                ]
            ],
            'nameList' => ['John Doe', 'J. Doe'],
            'emailList' => ['<EMAIL>'],
            'mobileList' => ['+91-**********'],
            'companyNameList' => [],
            'locationList' => ['Mumbai, India'],
            'leakData' => [],
            'companyInfoList' => [],
            'reportCreated' => date('Y-m-d H:i:s'),
            'userName' => (object)['displayName' => 'Test User']
        ];

        try {
            $html = $this->load->view('pdf_template', $testData, true);
            echo "<p><strong>✅ Template rendered successfully!</strong></p>";
            echo "<p><strong>HTML Length:</strong> " . strlen($html) . " characters</p>";
            echo "<details><summary>View HTML Source</summary><pre>" . htmlspecialchars($html) . "</pre></details>";
            
        } catch (Exception $e) {
            echo "<p><strong>❌ Template Error:</strong> " . $e->getMessage() . "</p>";
        }
    }

    /**
     * Main test dashboard
     */
    public function index() {
        echo "<h1>OSINT PDF Unification Test Dashboard</h1>";
        echo "<p>This dashboard helps test the unified PDF generation service.</p>";
        
        echo "<h2>🧪 Available Tests</h2>";
        echo "<ul>";
        echo "<li><a href='" . base_url('test_osint_pdf/test_database_pdf') . "'>Test Database PDF Generation</a></li>";
        echo "<li><a href='" . base_url('test_osint_pdf/test_raw_data_pdf') . "'>Test Raw Data PDF Generation</a></li>";
        echo "<li><a href='" . base_url('test_osint_pdf/compare_implementations') . "'>Compare Implementations</a></li>";
        echo "<li><a href='" . base_url('test_osint_pdf/test_template') . "'>Test Template Rendering</a></li>";
        echo "</ul>";

        echo "<h2>📊 System Status</h2>";
        echo "<ul>";
        echo "<li><strong>OsintPdfService:</strong> " . (class_exists('OsintPdfService') ? '✅ Loaded' : '❌ Not Found') . "</li>";
        echo "<li><strong>PDF Template:</strong> " . (file_exists(APPPATH . 'views/pdf_template.php') ? '✅ Found' : '❌ Not Found') . "</li>";
        echo "<li><strong>Upload Directory:</strong> " . (is_writable(FCPATH . 'uploads/telegram_osint/') ? '✅ Writable' : '❌ Not Writable') . "</li>";
        echo "</ul>";
    }
}
