<?php

class TelegramService {

    protected $CI;

    public function __construct()
    {
        $this->CI =& get_instance();
		$this->ccasApiUrl = $this->CI->config->item('CCASApiUrl', 'env');
        $this->CI->load->library('TelegramBot');
        $this->CI->load->helper('telegram');
        $this->CI->load->model('TelegramModel');
    }

    public function handleUserInput($chat_id, $telegram_id, $text)
    {

        $auth_token = $this->authToken($telegram_id);
		
        $user = $this->CI->TelegramModel->findOne(['telegramId' => $telegram_id]);

        $this->CI->telegrambot->sendMessage($chat_id, "Please wait, we're retrieving the information", 'Markdown');

        if (preg_match('/^pin\s+(\d{6})$/i', $text, $matches)) {
			$pincode = $matches[1];
			$this->searchPincode($chat_id, $pincode, $auth_token);
		} else if (preg_match('/^bank\s+(\d{9,18})\s+([A-Z]{4}0[A-Z0-9]{6})$/i', $text, $matches)) {
			$account_number = $matches[1];
			$ifsc_code = strtoupper($matches[2]);
			$this->fetchBankDetails($chat_id, $account_number, $ifsc_code, $user);
		} else if (preg_match('/^ifsc\s+([A-Z]{4}0[A-Z0-9]{6})$/i', $text, $matches)) {
			$ifsc_code = strtoupper($matches[1]);
			$this->fetchIfscInfo($chat_id, $ifsc_code, $auth_token);
		} else if (preg_match('/^location\s+([-+]?\d{1,2}\.\d+),\s*([-+]?\d{1,3}\.\d+)$/i', $text, $matches)) {

			$lat = $matches[1];
			$lng = $matches[2];			

			$this->searchLocation($chat_id, $lat, $lng);
		} else if (preg_match('/^isd\s+(\d{1,4})$/i', $text, $matches)) {
			$code = $matches[1];
			$this->fetchISDInfo($chat_id, $code, $auth_token);
		} else if (preg_match('/^std\s+(\d{3,5})$/i', $text, $matches)) {
			$code = $matches[1];
			$this->fetchSTDInfo($chat_id, $code, $auth_token);
		} else if (preg_match('/^toll\s+(\d{9,15})$/i', $text, $matches)) {
            $toll = $matches[1];
            $this->fetchTollFreeInfo($chat_id, $toll, $auth_token);
        } else if (preg_match('/^msp\s+(\d{10})$/i', $text, $matches)) {
            $mobile = $matches[1];
            $this->fetchMSPInfo($chat_id, $mobile, $auth_token);
        } else if (preg_match('/^smsid\s+([A-Z]{2}-[A-Z0-9]{6,7})$/i', $text, $matches)) {
            $senderId = strtoupper($matches[1]);
            $this->fetchSMSInfo($chat_id, $senderId, $auth_token);
        } else if (preg_match('/^short\s+(\d{3,8})$/i', $text, $matches)) {
            $shortCode = $matches[1];
            $this->fetchShortCodeInfo($chat_id, $shortCode, $auth_token);
        } else if (preg_match('/^shorten\s+([^\s]+)/i', $text, $matches)) {
            $longUrl = $matches[1];
            $this->fetchShortUrl($chat_id, $longUrl);
        } else if (preg_match('/^virtual\s+([\d\s]{10,100})$/i', $text, $matches)) {
            $numbersText = trim($matches[1]);
            $numbers = preg_split('/\s+/', $numbersText);

            // Optional: validate numbers format (must be digits and 10–13 characters)
            $validNumbers = array_filter($numbers, function($num) {
                return preg_match('/^\d{10,13}$/', $num);
            });

            if (!empty($validNumbers)) {
                $this->fetchVirtualNumberInfo($chat_id, $validNumbers);
            } else {
                $this->CI->telegrambot->sendMessage($chat_id, "⚠️ Invalid mobile number(s) format.", 'Markdown');
            }
        } else if (preg_match('/\bcin\s([A-Z0-9]{21})\b/i', $text, $match)) {
    		$cin = strtoupper($match[1]);
            $cinArray = [$cin];
			$this->fetchCompanyDetails($chat_id, $cinArray, $auth_token);
		} else if (preg_match('/^cname\s+(LLP|Private)\|(.{2,})$/i', $text, $matches)) {
            $companyType = $matches[1]; // LLP or Private
            $companyName = trim($matches[2]);        
            $this->fetchCompanyDetailsByName($chat_id, $companyType, $companyName, $auth_token);
        } else if (preg_match('/^din\s+(\d{8})$/i', $text, $matches)) {
            $din = $matches[1];
            $this->fetchDinDetails('din', $chat_id, $din, $auth_token);
        } else if (preg_match('/\bpan\s([A-Z0-9]{10})\b/i', $text, $match)) {
            $pan = $match[1];
            $this->fetchPanDetails($chat_id, $pan, $auth_token, $user);
        } else if (preg_match('/\bgst\s([A-Z0-9]{15})\b/i', $text, $match)) {
    		$gst = strtoupper($match[1]);
            $user_id = $telegram_id; // Or your internal user ID if mapped
            $this->fetchGstDetails($chat_id, $gst, $telegram_id);
		} else if (preg_match('/^nodal\s+(\S+)/i', $text, $match)) {
    		$companyName = $match[1];
            $this->fetchNodalDetails($chat_id, $companyName, $auth_token);
		} else if (preg_match('/^cellid\s+([A-Za-z0-9\-]{8,25})$/i', $text, $matches)) {
            $cgi = $matches[1];
            $this->fetchCellIdInfo($chat_id, $cgi, $auth_token);
        } else if (preg_match('/^aadhaar\s+(\d{12})$/i', $text, $matches)) {
            $aadhaar = $matches[1];
            $this->fetchAadhaarInfo($chat_id, $aadhaar, $user, $auth_token);
        } else if (preg_match('/^dl\s+([A-Z0-9]+)[\s|]+(\d{4}-\d{2}-\d{2})$/i', $text, $matches)) {
            $dl = strtoupper($matches[1]);
            $dob = $matches[2];
            $this->fetchDrivingLicenseInfo($chat_id, $dl, $dob, $user);
        } else if (preg_match('/^voter\s+([A-Z0-9]{10})$/i', $text, $matches)) {
            $voterId = strtoupper($matches[1]);
            $this->fetchVoterInfo($chat_id, $voterId, $user);
        } else if (preg_match('/^mnp\s+((\d{10})(,\d{10})*)$/i', $text, $matches)) {
            $mobile = $matches[1];
            $this->fetchMnpInfo($chat_id, $mobile);
        } else if (preg_match('/\bgmail\s+([a-zA-Z0-9._%+-]+@gmail\.com)\b/i', $text, $matches)) {
            $email = strtolower($matches[1]);
            $this->fetchGmailInfo($chat_id, $email, $auth_token);
        } else if (preg_match('/^vehicle\s+([A-Z]{2}\d{2}[A-Z]{1,3}\d{1,4})$/i', $text, $matches)) {
    		$vehicle = strtoupper($matches[1]);
    		$this->fetchVehicleInfo($chat_id, $vehicle, $user);
		} else if (preg_match('/^whatsapp\s(\d{10})$/i', $text, $matches)) {
            $mobile = $matches[1];
            $this->fetchWhatsappInfo($chat_id, $mobile, $user);
        } else if (preg_match('/^proxy\s+((?:\d{1,3}\.){3}\d{1,3}|(?:[a-fA-F0-9:]+))$/i', $text, $matches)) {
			$ipAddress = $matches[1];
			$this->handleProxyCheck($chat_id, $ipAddress, $auth_token);
		} else if (preg_match('/^ip\s+((?:\d{1,3}\.){3}\d{1,3})$/i', $text, $matches)) {
			$ipAddress = $matches[1];
			$this->fetchIPInfo($chat_id, $ipAddress, $auth_token);
		} else if (preg_match('/^imei\s+(\d{14,17})$/i', $text, $matches)) {
            $imei = $matches[1];
            $this->fetchImeiInfo($chat_id, $imei, $auth_token);
        } else if (preg_match('/^secondimei\s+(\d{14,17})$/i', $text, $matches)) {
            $imei = $matches[1];
            $this->fetchSecondImeiInfo($chat_id, $imei, $auth_token);
        } else if (preg_match('/^true\s+([\d,\s]+)$/i', $text, $matches)) {
            $mobiles = preg_split('/[\s,]+/', trim($matches[1]));
            $mobiles = array_filter($mobiles); // remove empty values

            $this->CI->load->model('truecallerModel');

            $truecallerSearchCredit = $user->truecallerSearchCredit;
            $truecallerDailySearch = $user->truecallerDailySearch;

            //Daily search by user
            $date = new DateTime("now");
            $curr_date = $date->format('Y-m-d ');
            $truecallerDailyCount = $this->CI->truecallerModel->count(['userId' => $user->id,'userType' => 'telegram','DATE(createdate)'=>$curr_date]);

            $dailySearchRem = $truecallerSearchCredit<$truecallerDailySearch ? $truecallerSearchCredit : $truecallerDailySearch;
            $dailySearchRem =  $dailySearchRem - $truecallerDailyCount;

            //check user daily limit is over
            if (count($mobiles) > $dailySearchRem) {
                $this->CI->telegrambot->sendMessage($chat_id, "❌ Your Daily Search Credit Over.", 'Markdown');
                return;
            }

            if (count($mobiles) > 10) {
                $this->CI->telegrambot->sendMessage($chat_id, "❌ Limit exceeded. You can only check up to *10 mobile numbers* at a time.", 'Markdown');
                return;
            }

            if ($truecallerSearchCredit < count($mobiles)) {
                $this->CI->telegrambot->sendMessage($chat_id, "❌ Your Search Credit Limit Reachead. Contact To Admin To Extend Your Limit.", 'Markdown');
                return;
            }

            foreach ($mobiles as $mobile) {
                if (preg_match('/^\d{10}$/', $mobile)) {
                    $this->fetchTruecallerInfo($chat_id, $mobile, $auth_token, $user->id);
                } else {
                    $this->CI->telegrambot->sendMessage($chat_id, "❌ Invalid mobile number: `$mobile`", 'Markdown');
                }
            }
        } else if (preg_match('/^osint\s+([\w@\.\+]{6,})$/i', $text, $matches)) {

            $query = trim($matches[1]);

            $this->CI->load->model('osintModel');

            $osintSearchCredit = $user->osintSearchCredit;
            $osintDailySearch = $user->osintDailySearch;

            //Daily search by user
            $date = new DateTime("now");
            $curr_date = $date->format('Y-m-d ');
            $osintDailyCount = $this->CI->osintModel->count(['userId' => $user->id,'userType' => 'telegram','DATE(createdOn)'=>$curr_date]);

            $osintDailySearchRem = $osintSearchCredit<$osintDailySearch ? $osintSearchCredit : $osintDailySearch;
            $osintDailySearchRem =  $osintDailySearchRem - $osintDailyCount;

            //check user daily limit is over
            if ($osintDailySearchRem == 0) {
                $this->CI->telegrambot->sendMessage($chat_id, "❌ Your Daily Search Credit Over.", 'Markdown');
                return;
            }

            if ($osintSearchCredit == 0) {
                $this->CI->telegrambot->sendMessage($chat_id, "❌ Your Search Credit Limit Reachead. Contact To Admin To Extend Your Limit.", 'Markdown');
                return;
            }

            $this->fetchOsintData($chat_id, $query, $user);

        } else if (preg_match('/^sdr\s+(\d{10,13})$/i', $text, $matches)) {
			$mobile = $matches[1];
			$this->fetchMobileSDRInfo($chat_id, $mobile);
		} else if (preg_match('/^case:\s*(.+)$/i', $text, $matches)) {
            $input = trim($matches[1]);
            $this->fetchLinkCreation($chat_id, $telegram_id, $input);
        } else if (preg_match('/^customize_(\d+?)_(https?:\/\/.+)$/', $text, $matches)) {
            $linkId = $matches[1];
            $domain = $matches[2];

            $this->customizeLink($chat_id, $linkId, $domain);
        } else if (preg_match('/view_report/i', $text, $matches)) {
            $this->viewLinks($chat_id, $telegram_id);
        } else if (preg_match('/^\/viewlink([A-Za-z0-9+\/=]+)$/', $text, $matches)) {
            $linkId = $matches[1];
            $this->viewLinkDetails($chat_id, $linkId, $telegram_id);
        } else if (preg_match('/^\/deletelink([A-Za-z0-9+\/=]+)$/', $text, $matches)) {
            $linkId = $matches[1];
            $this->deleteLink($chat_id, $linkId, $telegram_id);
        } else {
			writelog($text, 'uc');
			$this->CI->telegrambot->sendMessage($chat_id, "❓ Sorry, I didn't understand that command.\n\nType `/start` to begin or share your phone number to verify.");
		}
        return;
		
    }

    private function authToken($telegram_id) 
    {
        // Check if user exists
        $user = $this->CI->TelegramModel->findOne(['telegramId' => $telegram_id]);

        if($user->liswToken != null && $user->liswToken != '' && strtotime($user->liswTokenExpiration) > time()) {
            $ch = curl_init();

            $param = array(
                'token' => $user->liswToken,
            );

            $param = json_encode($param);
            curl_setopt($ch, CURLOPT_URL, 'https://mobileapi.lisw.in/api/Tac/IsTokenExpired');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $param);
            $headers = array();
            $headers[] = 'Content-Type: application/json';
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            $result = curl_exec($ch);

            curl_close($ch);

            $result = json_decode($result);
			
			//writelog($result, 'token expired');

            if($result->ResponseCode == 200 && $result->Message == null) {
                // Token is valid
                return $user->liswToken;
            }
        }

        $this->CI->load->model('SettingModel');

        // Get shared Telegram login password from settings
        $setting = $this->CI->SettingModel->findOne(['name' => 'telegram_login_password']);
        $pwd = $setting ? $setting->value : null;

        if (!$pwd) {
            return 'Unauthenticate';
        }

        $ch = curl_init();

        $param = array(
            'UserName' => $telegram_id,
            'Password' => $pwd
        );

        $param = json_encode($param);
        curl_setopt($ch, CURLOPT_URL, 'https://mobileapi.lisw.in/api/Token');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $param);
        $headers = array();
        $headers[] = 'Content-Type: application/json';
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $result = curl_exec($ch);
		
        if (curl_errno($ch)) {
            echo 'Error:' . curl_error($ch);
        }
        curl_close($ch);
        $result = json_decode($result);
	
        $token = (array_key_exists('token',$result)) ? $result->token : '';

        if( !$token || $token == '') {
            return 'Unauthenticate';
        }

        $this->CI->TelegramModel
            ->modify(["liswToken" => $token, 'liswTokenExpiration' => date('Y-m-d H:i:s', strtotime($result->Valid_Till)) ], ['id' => $user->id]);

        //writelog($token, 'lisw_token');

        return $token;
    }
    
    private function authUser($number = '9461101915')
    {
		$this->CI->load->model('userModel');
        $user = $this->CI->userModel->findOne(['mobile' => $number]);

        return $user;
    }

    public function searchPincode($chat_id, $pincode, $lisw_token)
    {
        $curlPost = "SortCode=AA-00test";

        $ch = curl_init($this->ccasApiUrl.'GetPincodeData?Pincode='.$pincode);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $curlPost);

        $headers = array();
        $headers[] = 'Authorization: Bearer '.$lisw_token;
        $headers[] = 'Content-Type: application/json';

        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
		
        $http_status = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $this->CI->telegrambot->sendMessage($chat_id, "⚠️ Failed to connect to server: " . curl_error($ch));
            curl_close($ch);
            return;
        }

        curl_close($ch);
		
		if ($http_status == 200) {

			$data = json_decode($response, true);

            $pincode_data = $data['Data'][0] ? $data['Data'][0] : null;

			if ($pincode_data) {
				// Assume you get post office details
				$message = "🏤 *Pincode Details:*\n";
				$message .= "*Pincode:* `$pincode`\n";
				$message .= "*Area:* `{$pincode_data['Circle_Name']}`\n";
                $message .= "*Office:* `{$pincode_data['Office_Name']}`\n";
				$message .= "*District:* `{$pincode_data['District']}`\n";
				$message .= "*State:* `{$pincode_data['StateName']}`\n";

				$this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
			} else {
				$this->CI->telegrambot->sendMessage($chat_id, "❌ No data found for Pincode `$pincode`.", 'Markdown');
			}
		} else {
			$this->CI->telegrambot->sendMessage($chat_id, "🚫 API returned error: HTTP $http_status");
		}
    }
	
	private function fetchBankDetails($chat_id, $bankacc, $ifsc, $user)
	{
        $user_id = $user->id;

		$this->CI->load->model('bankModel');
		$this->CI->load->model('VerificationTokenModel');

		$date = new DateTime("now");
		$check_date = $date->format('Y-m-d');

		// Get user's bank search limits from settings
		$this->CI->load->model('SettingModel');
		$userAccountType = $user->accountType; // demo / paid
		$field = 'bank_' . strtolower($userAccountType) . '_daily_credit';

		$setting = $this->CI->SettingModel->findOne([
			'name' => $field,
			'type' => 'ip_grabber'
		]);

		if (!$setting) {
			$this->CI->telegrambot->sendMessage($chat_id, "❌ Bank service not configured. Please contact admin.", 'Markdown');
			return;
		}

		$userlimit = (int)$setting->value;

		$bankDataCount = $this->CI->bankModel->count(['userType'=>'telegram', 'userId' => $user_id,'DATE(created_at)'=>$check_date]);

		if($bankDataCount >= $userlimit){
			$this->CI->telegrambot->sendMessage($chat_id, "❌ Daily limit over. Please try tomorrow.", 'Markdown');
			return;
		}

		$tokenRow = $this->CI->VerificationTokenModel->getActiveToken('bank');
		if (!$tokenRow) {
			$this->CI->telegrambot->sendMessage($chat_id, "❌ No active API token available.", 'Markdown');
			return;
		}
		$apiToken = $tokenRow->token;

		// First API: Get request_id
		$firstApiPayload = json_encode([
			"task_id" => "123",
			"group_id" => "1234",
			"data" => [
				"bank_account_no" => $bankacc,
				"bank_ifsc_code" => $ifsc
			]
		]);

        writeLog($firstApiPayload, 'bank-first-api-payload');

		$curl1 = curl_init();
		curl_setopt_array($curl1, array(
			CURLOPT_URL => 'https://indian-bank-account-verification.p.rapidapi.com/v3/tasks/async/verify_with_source/validate_bank_account',
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS => $firstApiPayload,
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json',
				'x-rapidapi-host: indian-bank-account-verification.p.rapidapi.com',
				'x-rapidapi-key: ' . $apiToken
			),
		));

		$firstApiResponse = curl_exec($curl1);

		curl_close($curl1);

		$firstApiData = json_decode($firstApiResponse, true);

		$request_id = isset($firstApiData['request_id']) ? $firstApiData['request_id'] : null;

		if (!$request_id) {
			$this->CI->VerificationTokenModel->setTokenStatus($tokenRow->id, 'inactive');
			$this->CI->telegrambot->sendMessage($chat_id, "❌ Unable to get request_id from Bank API.", 'Markdown');
			return;
		}

		$this->CI->VerificationTokenModel->incrementTokenUsage($tokenRow->id);

		// Wait before polling second API
		sleep(10);

		// Second API: Get bank data using request_id
		$curl2 = curl_init();
		curl_setopt_array($curl2, array(
			CURLOPT_URL => 'https://indian-bank-account-verification.p.rapidapi.com/v3/tasks?request_id=' . $request_id,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'GET',
			CURLOPT_HTTPHEADER => array(
				'x-rapidapi-host: indian-bank-account-verification.p.rapidapi.com',
				'x-rapidapi-key: ' . $apiToken
			),
		));
		$secondApiResponse = curl_exec($curl2);
		curl_close($curl2);

		$this->CI->VerificationTokenModel->incrementTokenUsage($tokenRow->id);

		$responses = json_decode($secondApiResponse, true);

		// Extract the result object
		$dataInsert = '';
		$status = 'FAIL';
		$extraction = null;
		// Updated extraction logic to match actual API response
		if (is_array($responses) && isset($responses[0]['result'])) {
			$extraction = $responses[0]['result'];
			$dataInsert = json_encode($extraction);
			$status = isset($responses[0]['status']) && $responses[0]['status'] == 'completed' ? 1 : $responses[0]['status'];
		}

		$inserData = [
			'userId' => $user_id,
			'userType' => 'telegram',
			'response' => $dataInsert,
			'status' => $status,
			'bank' => $bankacc,
			'ifsc' => $ifsc,
		];

		$this->CI->bankModel->attach($inserData);

		// Format output for Telegram using the new structure
		if ($extraction) {
			$finalMessage = "🏦 *Bank Account Information for:* `$bankacc`\n\n";
			$finalMessage .= "*Name:* `" . (isset($extraction['name_at_bank']) && $extraction['name_at_bank'] ? $extraction['name_at_bank'] : 'N/A') . "`\n";
			$finalMessage .= "*Bank A/c No:* `" . (isset($extraction['bank_account_number']) && $extraction['bank_account_number'] ? $extraction['bank_account_number'] : 'N/A') . "`\n";
			$finalMessage .= "*IFSC Code:* `" . (isset($extraction['ifsc_code']) && $extraction['ifsc_code'] ? $extraction['ifsc_code'] : 'N/A') . "`\n";
			$finalMessage .= "*A/c exists:* `" . (isset($extraction['account_exists']) && $extraction['account_exists'] ? $extraction['account_exists'] : 'N/A') . "`\n";
			$finalMessage .= "*Amount Deposited:* `" . (isset($extraction['amount_deposited']) && $extraction['amount_deposited'] ? $extraction['amount_deposited'] : 'N/A') . "`\n";
			$finalMessage .= "*Status:* `" . (isset($extraction['status']) && $extraction['status'] ? $extraction['status'] : 'N/A') . "`\n";

			$this->CI->telegrambot->sendMessage($chat_id, $finalMessage, 'Markdown');
		} else {
            writeLog('Bank API extraction failed. $responses: ' . print_r($firstApiData, true) . ' $secondApiResponse: ' . print_r($responses, true), 'bank-api-error');
			$this->CI->telegrambot->sendMessage($chat_id, "❌ Some issue occurred. Please contact admin", 'Markdown');
		}
	}

    private function fetchIfscInfo($chat_id, $ifsc, $lisw_token)
    {

        $ch = curl_init($this->ccasApiUrl.'GetBankDetailsByIFSCCode?IFSCCode='.$ifsc);
		$curlPost = "SortCode=AA-00test";

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $curlPost);

        $headers = array();
        $headers[] = 'Authorization: Bearer '.$lisw_token;
        $headers[] = 'Content-Type: application/json';

        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
		
        $http_status = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $this->CI->telegrambot->sendMessage($chat_id, "⚠️ Failed to connect to server: " . curl_error($ch));
            curl_close($ch);
            return;
        }

        curl_close($ch);
		
		if ($http_status == 200) {

			$data = json_decode($response, true);

            $ifsc_data = $data['Data'][0] ? $data['Data'][0] : null;

			if ($ifsc_data) {

				$message = "🏦 *IFSC Details:*\n";
                $message .= "*IFSC Code:* `$ifsc`\n";
                $message .= "*Bank Name:* `{$ifsc_data['Bank']}`\n";
                $message .= "*Branch:* `{$ifsc_data['Branch']}`\n";
                $message .= "*Address:* `{$ifsc_data['Address']}`\n";
                $message .= "*City1:* `{$ifsc_data['City1']}`\n";
                $message .= "*City2:* `{$ifsc_data['city2']}`\n";
                $message .= "*State:* `{$ifsc_data['State']}`\n";
                $message .= "*Contact:* `{$ifsc_data['phone']}`\n";
                $message .= "*STD Code:* `{$ifsc_data['STDCODE']}`\n";

				$this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
			} else {
				$this->CI->telegrambot->sendMessage($chat_id, "❌ No data found for IFSC `$ifsc`.", 'Markdown');
			}
		} else {
			$this->CI->telegrambot->sendMessage($chat_id, "🚫 API returned error: HTTP $http_status");
		}
    }

    private function searchLocation($chat_id, $lat, $lng)
    {
        /*list($lat, $lng) = explode(',', $latlong);

        if (round($lat, 2) == 28.61 && round($lng, 2) == 77.21) {
            $message = "📍 New Delhi, India.";
        } else {
            $message = "❌ Could not find a location for `$latlong`.";
        }*/

		$link = "https://www.google.com/maps?q={$lat},{$lng}";
		
		$message = "📍 *Location Detected!*\n\n";
		$message .= "Latitude: `{$lat}`\n";
		$message .= "Longitude: `{$lng}`\n\n";
		$message .= "[🗺️ Open in Google Maps]($link)";
		
        $this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
    }

	private function fetchISDInfo($chat_id, $isdCode, $lisw_token)
    {
        $url = $this->ccasApiUrl . 'GetCountryInformationByISD?ISDCode=' . urlencode($isdCode);
        
        $data = $this->makeApiCall($url, $lisw_token);

        $isdData = $data[0] ? $data[0] : null;

        if ($isdData) {
            $message = "🌍 *Country Information:*\n";
            $message .= "*Country:* `{$isdData->CountryName}`\n";
            $message .= "*ISD Code:* `+{$isdData->CountryCode}`\n";
        } else {
            $message = "❌ No country found for ISD code `$isdCode`.";
        }

        $this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
    }

    private function fetchSTDInfo($chat_id, $stdCode, $lisw_token)
    {
        $url = $this->ccasApiUrl . 'GetCityNameAndStateNameBySTDCode?STDCode=' . urlencode($stdCode);
        $data = $this->makeApiCall($url, $lisw_token);

        $stdData = $data[0] ? $data[0] : null;

		if ($stdData) {
            $message = "🏙️ *City Information:`$stdCode`*\n";
            $message .= "*City:* `{$stdData->CityName}`\n";
            $message .= "*State:* `{$stdData->StateName}`\n";
        } else {
            $message = "❌ No city found for STD code `$stdCode`.";
        }

        $this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
    }

    private function fetchTollFreeInfo($chat_id, $tollFreeNumber, $lisw_token)
    {

        $curlPost = json_encode(["TallFreeNumber" => $tollFreeNumber]);

        $cURLConnection = curl_init('https://mobileapi.lisw.in/api/Tac/GetTollFreeCodeInfoByNumber');
        curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
        curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);

        $headers = [
            'Authorization: Bearer ' . $lisw_token,
            'Cache-Control: no-cache',
            'Content-Type: application/json'
        ];
        curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);
    
        $result = curl_exec($cURLConnection);
		
		//writelog($result, 'telegram');

        if (curl_errno($cURLConnection)) {
            $error = curl_error($cURLConnection);
            curl_close($cURLConnection);
            $this->CI->telegrambot->sendMessage($chat_id, "❌ cURL Error: `$error`", 'Markdown');
            return;
        }
    
        curl_close($cURLConnection);
        $response = json_decode($result);
    
        if (!empty($response->Data) && is_array($response->Data)) {
			$data = $response->Data[0];

            $message = "*📱 Toll Free Info:*\n";
			
			if (!empty($data->shortentityname)) {
                $message .= "*Purpose:* `{$data->shortentityname}`\n";
            }
			
			if (!empty($data->Category)) {
                $message .= "*Category:* `{$data->Category}`\n";
            }
			
			if (!empty($data->tollfreeentityname)) {
                $message .= "*Name:* `{$data->tollfreeentityname}`\n";
            }

            if (!empty($data->tollfreeentitytype)) {
                $message .= "*Type:* `{$data->tollfreeentitytype}`\n";
            }

            if (!empty($data->Website) && strtolower($data->Website) !== 'null') {
                $message .= "*Website:* [Visit Site](https://{$data->Website})\n";
            }
    
            $this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
        } else {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ No data found for toll-free number `{$tollFreeNumber}`.", 'Markdown');
        }
    }

    private function fetchShortCodeInfo($chat_id, $shortCode, $lisw_token)
    {

        $curlPost = json_encode(["ShortCode"=>$shortCode]);

        $cURLConnection = curl_init('https://mobileapi.lisw.in/api/Tac/GetShortCode');
        curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
        curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);

        $headers = [
            'Authorization: Bearer ' . $lisw_token,
            'Cache-Control: no-cache',
            'Content-Type: application/json'
        ];
        curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);
    
        $result = curl_exec($cURLConnection);

        if (curl_errno($cURLConnection)) {
            $error = curl_error($cURLConnection);
            curl_close($cURLConnection);
            $this->CI->telegrambot->sendMessage($chat_id, "❌ cURL Error: `$error`", 'Markdown');
            return;
        }
    
        curl_close($cURLConnection);
        $response = json_decode($result);
    
        if (!empty($response->Data) && is_array($response->Data)) {
			$data = $response->Data[0];

			$message = "*📱 Short Code Info:*\n";
			$message .= "*Purpose:* `{$data->shortentityname}`\n";

			if (!empty($data->Category)) {
				$message .= "*Category:* `{$data->Category}`\n";
			}

			if (!empty($data->Website) && strtolower($data->Website) !== 'null') {
				$message .= "*Website:* [Visit Site](https://{$data->Website})\n";
			}
    
            $this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
        } else {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ No data found for toll-free number `{$tollFreeNumber}`.", 'Markdown');
        }
    }

    private function fetchVirtualNumberInfo($chat_id, $numbers)
    {
        $url = 'https://msg.ccas.in/api/numCheckr/check_number';

        // Prepare the string (space-separated)
        $queryString = implode(' ', $numbers);

        // Prepare the headers
        $headers = [
            'Authorization: Bearer ' . $this->authUser()->client_token,
            //'Content-Type: application/json'
        ];

        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => ['queryString' => $queryString],
            CURLOPT_HTTPHEADER => $headers,
        ]);

        $response = curl_exec($ch);

        curl_close($ch);

        $decoded = json_decode($response, true);

        if (!empty($decoded['data'])) {
            $message = "*📱 Virtual Number Check Result:*\n";

            foreach ($decoded['data'] as $item) {
                $phone     = $item['Phone'] ? $item['Phone'] : 'N/A';
                $isValid   = $item['Number valid'] ? $item['Number valid'] : 'Unknown';
                $isVirtual = $item['Virtual Number'] ? $item['Virtual Number'] : 'Unknown';
                $country   = $item['Country Name'] ? $item['Country Name'] : 'Unknown';
                $company   = $item['Company Name'] ? $item['Company Name'] : 'Unknown';

                $message .= "\n*Phone:* `$phone`\n";
                $message .= "*Valid:* `$isValid`\n";
                $message .= "*Virtual:* `$isVirtual`\n";
                $message .= "*Country:* `$country`\n";
                $message .= "*Operator:* `$company`\n";
            }

            $this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
        } else {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ No result found or invalid numbers.", 'Markdown');
        }

    }

    public function fetchMSPInfo($chat_id, $mobileNumber, $lisw_token)
    {
        if (!preg_match('/^\d{10}$/', $mobileNumber)) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Invalid mobile number format. It must be 10 digits.", 'Markdown');
            return;
        }

        $mobile_code = mb_substr($mobileNumber, 0, 5);
        $curlPost = "SortCode=AA-00test";

        $url = 'https://mobileapi.lisw.in/api/Tac/GetServiceProviderByMobileCode?MobileCode=' . $mobile_code;

        $cURLConnection = curl_init($url);
        curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
        curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);

        $headers = [
            'Authorization: Bearer ' . $lisw_token,
            'Cache-Control: no-cache'
        ];
        curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);

        $result = curl_exec($cURLConnection);

        if (curl_errno($cURLConnection)) {
            $error = curl_error($cURLConnection);
            curl_close($cURLConnection);
            $this->CI->telegrambot->sendMessage($chat_id, "❌ cURL Error: `$error`", 'Markdown');
            return;
        }

        curl_close($cURLConnection);
        $response = json_decode($result);

        if (!empty($response->Data) && is_array($response->Data)) {
			$data = $response->Data[0]; // use first item from array

			$message = "*📱 Mobile Service Provider Info:*\n";
			$message .= "*Mobile:* `{$mobileNumber}`\n";
			$message .= "*Service Provider:* `{$data->serviceProvider}`\n";
			$message .= "*State:* `{$data->State}`\n";
			$message .= "*Mobile Code:* `{$data->MobileCode}`\n";

			$this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
		} else {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ No provider data found for `{$mobileNumber}`.", 'Markdown');
        }
    }

    private function fetchShortUrl($chat_id, $longUrl)
    {
        // Validate URL
        /*if (!filter_var($longUrl, FILTER_VALIDATE_URL)) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Invalid URL. Please enter a valid link starting with http or https.", 'Markdown');
            return;
        }*/
		
		if (!preg_match('~^https?://~i', $longUrl)) {
			$longUrl = 'https://' . $longUrl;
		}

        // Generate short code and URL
        $shortCode = bin2hex(openssl_random_pseudo_bytes(8)); // 16-char code
        $shortLink = 'https://www.G00Gl.CO.IN/us/' . $shortCode;

        // Insert into database
        $this->CI->load->model('UrlShortenerModel');
        $shortenerData = [
            'userId'     => 1, // Or use chat_id / Telegram ID if you want to associate
            'longUrl'    => $longUrl,
            'shortUrl'   => $shortLink,
            'shortCode'  => $shortCode
        ];

        if ($this->CI->UrlShortenerModel->attach($shortenerData)) {
            $message = "*🔗 URL Shortened Successfully!*\n";
            $message .= "*Original:* [Visit]({$longUrl})\n";
            $message .= "*Short:* [{$shortLink}]({$shortLink})";
            $this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
        } else {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Failed to shorten the URL. Please try again later.", 'Markdown');
        }
    }

    public function fetchSMSInfo($chat_id, $shortCode, $lisw_token)
    {
        if (!preg_match('/^[A-Z]{2}-[A-Z0-9]{6,7}$/', $shortCode)) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Invalid Sender ID format. It must be 9 alphanumeric characters.", 'Markdown');
            return;
        }

        //'/^smsid\s+([A-Z]{2}-[A-Z0-9]{6,7})$/i'

        $curlPost = []; // Empty post as per API

        $url = 'https://mobileapi.lisw.in/api/Tac/SmsSenderIdDetails?SortCode=' . urlencode($shortCode);

        $cURLConnection = curl_init($url);
        curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
        curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);

        $headers = [
            'Authorization: Bearer ' . $lisw_token,
            'Cache-Control: no-cache'
        ];
        curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);

        $result = curl_exec($cURLConnection);

        if (curl_errno($cURLConnection)) {
            $error = curl_error($cURLConnection);
            curl_close($cURLConnection);
            $this->CI->telegrambot->sendMessage($chat_id, "❌ cURL Error: `$error`", 'Markdown');
            return;
        }

        curl_close($cURLConnection);
        $response = json_decode($result);
        //$data = $response->Data ? $response->Data : [];
		
		if (!empty($response->Data) && is_array($response->Data)) {
			$data = $response->Data[0]; // Use the first entry

			$message = "*📤 Bulk SMS Sender ID Info:*\n";
			$message .= "*Sender ID:* `{$shortCode}`\n";
			$message .= "*Operator:* `{$data->OperatorName}`\n";
			$message .= "*Region:* `{$data->OperatorRegion}`\n";
			$message .= "*Entity:* `{$data->EntityName}`\n";

			$this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
		} else {
			$this->CI->telegrambot->sendMessage($chat_id, "❌ No data found for Sender ID `{$shortCode}`.", 'Markdown');
		}
        
    }

    /* Company Methods */

    private function fetchCompanyDetails($chat_id, array $cinNumbers, $lisw_token) {

        $url = 'https://mobileapi.lisw.in/api/Tac/GetCINDataAPI';
        $params = ['Cin' => $cinNumbers];

        $headers = [
            'Authorization: Bearer ' . $lisw_token,
            'Content-Type: application/json'
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));

        $result = curl_exec($ch);
        if (curl_errno($ch)) {
            log_message('error', 'CURL Error: ' . curl_error($ch));
            return null;
        }

        curl_close($ch);

        $decoded = json_decode($result, true);

        $companyData = $decoded[0]['cindata']['data']['companyData'];
		$directors = $decoded[0]['getDirectorModelDataInfos']['data'];

        $message = "*🏢 Company Information:*\n";
        $message .= "*Name:* `{$companyData['company']}`\n";
        $message .= "*CIN:* `{$companyData['cin']}`\n";
        $message .= "*Category:* `{$companyData['companyCategory']}`\n";
        $message .= "*Subcategory:* `{$companyData['companySubcategory']}`\n";
        $message .= "*Class:* `{$companyData['classOfCompany']}`\n";
        $message .= "*Status:* `{$companyData['status']}` / `{$companyData['companyStatus']}`\n";
        $message .= "*Listed:* `{$companyData['whetherListedOrNot']}`\n";
        $message .= "*ROC Code:* `{$companyData['rocCode']}`\n";
        $message .= "*Division:* `{$companyData['mainDivisionDescription']}`\n";
        $message .= "*Date of Incorporation:* `{$companyData['dateOfIncorporation']}`\n";
        $message .= "*Last AGM:* `{$companyData['dateOfLastAGM']}`\n";
        $message .= "*Strike Off/Transfer Date:* `{$companyData['strikeOff_amalgamated_transferredDate']}`\n";
        $message .= "*Email:* `{$companyData['emailAddress']}`\n";
        $message .= "*Authorized Capital:* ₹`{$companyData['authorisedCapital']}`\n";
        $message .= "*Registration No:* `{$companyData['registrationNumber']}`\n";
        $message .= "*Longitude:* `{$companyData['longitude']}`\n";
		
		$directorMessage = $this->formatDirectorList($directors);

        $this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
		$this->CI->telegrambot->sendMessage($chat_id, $directorMessage, 'Markdown');
    }

    private function fetchCompanyDetailsByName($chat_id, $companyType, $companyName, $lisw_token) {
		
		$companyType = strtolower($companyType); // Normalize input
		
		$map = [
			'llp' => 'LLP',
			'private' => 'Private',
		];

		$convertedCompanyType = $map[$companyType] ? $map[$companyType] : ucfirst($companyType); // Fallback for unknown types


        $url = 'https://mobileapi.lisw.in/api/Tac/CompanynameSearch';
        $params = ['CompanyType'=> $convertedCompanyType, 'CompanyName'=>$companyName];

        $headers = [
            'Authorization: Bearer ' . $lisw_token,
            'Content-Type: application/json'
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));

        $result = curl_exec($ch);
		
        if (curl_errno($ch)) {
            log_message('error', 'CURL Error: ' . curl_error($ch));
            return null;
        }

        curl_close($ch);

        $decoded = json_decode($result, true);

        if ($result && isset($decoded[0]['cindata']['data']['companyData'])) {

            $companyData = $decoded[0]['cindata']['data']['companyData'];
            $directors = $decoded[0]['getDirectorModelDataInfos']['data'];

            $message = "*🏢 Company Information:*\n";
            $message .= "*Name:* `{$companyData['company']}`\n";
            $message .= "*CIN:* `{$companyData['cin']}`\n";
            $message .= "*Category:* `{$companyData['companyCategory']}`\n";
            $message .= "*Subcategory:* `{$companyData['companySubcategory']}`\n";
            $message .= "*Class:* `{$companyData['classOfCompany']}`\n";
            $message .= "*Status:* `{$companyData['status']}` / `{$companyData['companyStatus']}`\n";
            $message .= "*Listed:* `{$companyData['whetherListedOrNot']}`\n";
            $message .= "*ROC Code:* `{$companyData['rocCode']}`\n";
            $message .= "*Division:* `{$companyData['mainDivisionDescription']}`\n";
            $message .= "*Date of Incorporation:* `{$companyData['dateOfIncorporation']}`\n";
            $message .= "*Last AGM:* `{$companyData['dateOfLastAGM']}`\n";
            $message .= "*Strike Off/Transfer Date:* `{$companyData['strikeOff_amalgamated_transferredDate']}`\n";
            $message .= "*Email:* `{$companyData['emailAddress']}`\n";
            $message .= "*Authorized Capital:* ₹`{$companyData['authorisedCapital']}`\n";
            $message .= "*Registration No:* `{$companyData['registrationNumber']}`\n";
            $message .= "*Longitude:* `{$companyData['longitude']}`\n";
            
            $directorMessage = $this->formatDirectorList($directors);

            $this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
            $this->CI->telegrambot->sendMessage($chat_id, $directorMessage, 'Markdown');
        } else {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ No data found for company name `$companyName`.", 'Markdown');
			return;
        }
    }
	
    private function fetchDinDetails($type, $chat_id, $dinNumbers, $lisw_token) {
		
		if($type == 'pan') {
			$dinNumbers = strtoupper($dinNumbers);
		}

    	$curlPost = [
            "Din" => $dinNumbers // Accepts array of DINs
        ];

    	$cURLConnection = curl_init('https://mobileapi.lisw.in/api/Tac/GetDinPanDataAPI');
		
        curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, json_encode($curlPost));
        curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);

        $headers = array();
        $headers[] = 'Authorization: Bearer ' . $lisw_token;
        $headers[] = 'Cache-Control: no-cache';
        $headers[] = 'Content-Type: application/json';
        curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);

        $result = curl_exec($cURLConnection);

        if (curl_errno($cURLConnection)) {
            $error = curl_error($cURLConnection);
            curl_close($cURLConnection);
            $this->CI->telegrambot->sendMessage($chat_id, "❌ cURL Error: `$error`", 'Markdown');
            return;
        }

        curl_close($cURLConnection);
		
    	$response = json_decode($result);
		
		writeLog($result, 'din-pan-api-response');

        if ($response && !empty($response->data)) {
			$dataObj = json_decode($response->data, true)['data'];
			
            if($type == 'din') {
				$message  = "*📄 DIN Holder Details:*\n";
			} else {
				$message  = "*📄 PAN Holder Details:*\n";
			}
			
            $message .= "*Name:* Not Provided\n"; // No name in the response
            $message .= "*Nationality:* `{$dataObj['nationality']}`\n";
            $message .= "*Email:* `{$dataObj['emailId']}`\n";
            $message .= "*Mobile:* `{$dataObj['mobileNo']}`\n";
            $message .= "*Address:*\n";
            $message .= "`{$dataObj['addressLine1']}`\n";
            if (!empty($dataObj['addressLine2']) && $dataObj['addressLine2'] !== "null") {
                $message .= "`{$dataObj['addressLine2']}`\n";
            }
            $message .= "`{$dataObj['city']}, {$dataObj['state']} - {$dataObj['pinCode']}`\n";
            $message .= "*Country:* `{$dataObj['country']}`\n";

            $this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
        } else {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ No data found `$dinNumbers`.", 'Markdown');
        }
        
    }

    private function fetchPanDetails($chat_id, $pan, $lisw_token, $user) {		
        $user_id = $user->id;
        $pan = strtoupper($pan);

        $this->CI->load->model('panModel');

        $date = new DateTime("now");
        $check_date = $date->format('Y-m-d');

        // Get user's PAN search limits from settings
        $this->CI->load->model('SettingModel');
        $userAccountType = $user->accountType; // demo / paid
        $field = 'pan_' . strtolower($userAccountType) . '_daily_credit';

        $setting = $this->CI->SettingModel->findOne([
            'name' => $field,
            'type' => 'ip_grabber'
        ]);

        if (!$setting) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ PAN service not configured. Please contact admin.", 'Markdown');
            return;
        }

        $userlimit = (int)$setting->value;

        $truecallerDataCount = $this->CI->panModel->count(['userId' => $user_id,'DATE(created_at)'=>$check_date]);

        if($truecallerDataCount >= $userlimit){
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Daily limit over. Please try tomorrow.", 'Markdown');
            return;
        }

        // ----------- FIRST API: LISW API -----------
        $curlPost = [
            "Din" => $pan
        ];

        $cURLConnection = curl_init('https://mobileapi.lisw.in/api/Tac/GetDinPanDataAPI');

        curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, json_encode($curlPost));
        curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);

        $headers = array();
        $headers[] = 'Authorization: Bearer ' . $lisw_token;
        $headers[] = 'Cache-Control: no-cache';
        $headers[] = 'Content-Type: application/json';

        curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);

        $result = curl_exec($cURLConnection);

        writeLog($result, 'pan-api-response');

        if (curl_errno($cURLConnection)) {
            $error = curl_error($cURLConnection);
            curl_close($cURLConnection);
            $this->CI->telegrambot->sendMessage($chat_id, "❌ cURL Error: `$error`", 'Markdown');
            return;
        }

        curl_close($cURLConnection);

        $response = json_decode($result);

        $dataObj = null;

        if ($response && !empty($response->data)) {
            $dataObj = json_decode($response->data, true)['data'];
        }

        if (isset($dataObj) && !empty($dataObj) ) {

            $dataInsert = $response->data;
            $status = 1;

            $inserData = [
                'userId' => $user_id,
                'userType' => 'telegram',
                'response' => $dataInsert,
                'status' => $status,
                'pan_number' => $pan,
            ];

            $this->CI->panModel->attach($inserData);

            $message  = "*📄 PAN Holder Details (Director):*\n";
            $message .= "*Owner Type:* `Director`\n";
            $message .= "*Nationality:* `" . (isset($dataObj['nationality']) ? $dataObj['nationality'] : 'N/A') . "`\n";
            $message .= "*Email:* `" . (isset($dataObj['emailId']) ? $dataObj['emailId'] : 'N/A') . "`\n";
            $message .= "*Mobile:* `" . (isset($dataObj['mobileNo']) ? $dataObj['mobileNo'] : 'N/A') . "`\n";
            $message .= "*Address:*\n";
            $message .= "`" . (isset($dataObj['addressLine1']) ? $dataObj['addressLine1'] : '') . "`\n";
            if (!empty($dataObj['addressLine2']) && $dataObj['addressLine2'] !== "null") {
                $message .= "`{$dataObj['addressLine2']}`\n";
            }
            $message .= "`" . (isset($dataObj['city']) ? $dataObj['city'] : '') . ", " . (isset($dataObj['state']) ? $dataObj['state'] : '') . " - " . (isset($dataObj['pinCode']) ? $dataObj['pinCode'] : '') . "`\n";
            $message .= "*Country:* `" . (isset($dataObj['country']) ? $dataObj['country'] : 'N/A') . "`\n";

            $this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
            return;
        }

        // ----------- SECOND API: PAN Card Verification (RapidAPI) -----------

        $this->CI->load->model('VerificationTokenModel');

        // Get all active tokens for PAN service
        $tokenRows = $this->CI->VerificationTokenModel->getAllActiveTokens('pan');
        $tokenFound = false;
        $panApiData = null;
        $usedTokenRow = null;

        $status = 'FAIL';
        $dataInsert = '';

        $thirdApiPayload = json_encode([
            "PAN" => $pan
        ]);

        writeLog($thirdApiPayload, 'pan-api-payload');

        foreach ($tokenRows as $tokenRow) {

            $apiToken = $tokenRow->token;

            $curl3 = curl_init();

            curl_setopt_array($curl3, array(
                CURLOPT_URL => 'https://pan-card-verification-at-lowest-price.p.rapidapi.com/verification/marketing/pan',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => $thirdApiPayload,
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json',
                    'x-rapidapi-host: pan-card-verification-at-lowest-price.p.rapidapi.com',
                    'x-rapidapi-key: ' . $apiToken
                ),
            ));

            $panApiResponse = curl_exec($curl3);

            curl_close($curl3);

            writeLog($panApiResponse, 'pan-api-response');

            $panApiData = json_decode($panApiResponse, true);

            if (isset($panApiData['pan']) && !empty($panApiData['pan'])) {
                $tokenFound = true;
                $usedTokenRow = $tokenRow;

                $status = 1;
                $dataInsert = json_encode($panApiData);

                break; // Success, stop trying other tokens
            } else {
                $this->CI->VerificationTokenModel->setTokenStatus($tokenRow->id, 'inactive');
                // Try next token
            }
        }

        if (!$tokenFound) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ All tokens failed.", 'Markdown');
            return;
        }

        // Use the working token for the rest of the process
        $this->CI->VerificationTokenModel->incrementTokenUsage($usedTokenRow->id);

        $inserData = [
            'userId' => $user_id,
            'userType' => 'telegram',
            'owner_type' => 'individual',
            'response' => $dataInsert,
            'status' => $status,
            'pan_number' => $pan,
        ];

        $this->CI->panModel->attach($inserData);

        if (isset($panApiData['pan']) && !empty($panApiData['pan'])) {

            $message  = "*📄 PAN Holder Details (Individual):*\n";
            $message .= "*Owner Type:* `Individual`\n";
            $message .= "*Name:* `" . (isset($panApiData['name_provided']) ? $panApiData['name_provided'] : 'N/A') . "`\n";
            $message .= "*Registered Name:* `" . (isset($panApiData['registered_name']) ? $panApiData['registered_name'] : 'N/A') . "`\n";
            $message .= "*PAN Card Name:* `" . (isset($panApiData['name_pan_card']) ? $panApiData['name_pan_card'] : 'N/A') . "`\n";
            $message .= "*PAN:* `" . (isset($panApiData['pan']) ? $panApiData['pan'] : 'N/A') . "`\n";
            $message .= "*Status:* `" . (isset($panApiData['pan_status']) ? $panApiData['pan_status'] : 'N/A') . "`\n";
            $message .= "*Type:* `" . (isset($panApiData['type']) ? $panApiData['type'] : 'N/A') . "`\n";
            $message .= "*Aadhaar Status:* `" . (isset($panApiData['aadhaar_seeding_status']) ? $panApiData['aadhaar_seeding_status'] : 'N/A') . "`\n";
            $message .= "*Aadhaar Description:* `" . (isset($panApiData['aadhaar_seeding_status_desc']) ? $panApiData['aadhaar_seeding_status_desc'] : 'N/A') . "`\n";

            $this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
        } else {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ No Data Found", 'Markdown');
        }

    }

    private function fetchGstDetails($chat_id, $gst, $user_id)
	{
		$ch = curl_init();

        curl_setopt_array($ch, array(
            CURLOPT_URL => 'https://razorpay.com/api/gstin/'.$gst,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
        ));

		$response = curl_exec($ch);

		$http_status = curl_getinfo($ch, CURLINFO_HTTP_CODE);

		curl_close($ch);

		if ($http_status == 200 && $response) {

			$data = json_decode($response);

			if (isset($data->enrichment_details)) {
                
				$msg = "🏦 *GST Information:*\n";
				
				$data = json_decode($response);

                $details = $data->enrichment_details->online_provider->details;

$legal_name = isset($details->legal_name->value) ? $details->legal_name->value : 'N/A';
$gstin = isset($details->gstin->value) ? $details->gstin->value : 'N/A';
$constitution = isset($details->constitution->value) ? $details->constitution->value : 'N/A';
$central_jurisdiction = isset($details->central_jurisdiction->value) ? $details->central_jurisdiction->value : 'N/A';
$primary_address = isset($details->primary_address->value) ? $details->primary_address->value : 'N/A';
$registration_date = isset($details->registration_date->value) ? $details->registration_date->value : 'N/A';
$status = isset($details->status->value) ? $details->status->value : 'N/A';
$tax_payer_type = isset($details->tax_payer_type->value) ? $details->tax_payer_type->value : 'N/A';
$trade_name = isset($details->trade_name->value) ? $details->trade_name->value : 'N/A';
$state_jurisdiction = isset($details->state_jurisdiction->value) ? $details->state_jurisdiction->value : 'N/A';


                $msg .= "*Legal Name Of Business:* {$legal_name}\n";
                $msg .= "*GSTIN:* {$gstin}\n";
                $msg .= "*Constitution Of Business:* {$constitution}\n";				

                $msg .= "*GSTIN Status:* {$status}\n";
                $msg .= "*Date Of Registration:* {$registration_date}\n";
                $msg .= "*Centre Jurisdiction:* {$central_jurisdiction}\n";
                $msg .= "*Place Of Business:* {$primary_address}\n";
                $msg .= "*Taxpayer Type:* {$tax_payer_type}\n";
                $msg .= "*Trade Name:* {$trade_name}\n";
                $msg .= "*State Jurisdiction Code:* {$state_jurisdiction}\n";

				$this->CI->telegrambot->sendMessage($chat_id, $msg, 'Markdown');

			} else {
				$error_msg = isset($responses->response) ? $responses->response : "Some issue occurred. Please contact admin.";
				$this->CI->telegrambot->sendMessage($chat_id, "❌ $error_msg");
				return ;
			}

		} else {
			$this->CI->telegrambot->sendMessage($chat_id, "🚫 API returned error: HTTP $http_status");
			return ;
		}
	}

    private function fetchNodalDetails($chat_id, $companyName, $lisw_token) {

        $url = 'https://mobileapi.lisw.in/api/Tac/GetNodalByCompanyName';

        $params = ["Company"=>$companyName];

        $headers = [
            'Authorization: Bearer ' . $lisw_token,
            'Content-Type: application/json'
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));

        $result = curl_exec($ch);
        if (curl_errno($ch)) {
            log_message('error', 'CURL Error: ' . curl_error($ch));
            return null;
        }

        curl_close($ch);

        $decoded = json_decode($result, true);

        if(empty($decoded['Data'])) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ No data found for company name `$companyName`.", 'Markdown');
            return;
        }

        $message = $this->formatNodalList($decoded['Data']);

        $this->CI->load->library('pdf');

        $fileName = "nodal_" . time() . ".pdf";
        $filePath = FCPATH . "uploads/telegram_osint/" . $fileName;
        $publicUrl = base_url("uploads/telegram_osint/" . $fileName);
        
        $this->CI->pdf->telegramLink($message, $filePath, 'Cligence');

        // Send link to user
        $msg = "📄 *Your Nodal report is ready:*\n\n[Download PDF]($publicUrl)";
        $this->CI->telegrambot->sendDocument($chat_id, $filePath);
        return ;

		//$this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
    }
	
	private function formatDirectorList($directors)
	{
		if (empty($directors)) {
			return "👥 No director data available.";
		}

		$message = "*👥 Directors List:*\n";

		foreach ($directors as $index => $director) {
			$nameParts = array_filter([
				$director['firstName'] ? $director['firstName'] : '',
				$director['midName'] !== 'null' ? $director['midName'] : '',
				$director['lastName'] ? $director['lastName'] : '',				
			]);
			$fullName = implode(' ', $nameParts);

			$message .= "\n*".($index + 1) . ". {$fullName}*\n";
			$message .= "• DIN: `{$director['din']}`\n";
			$message .= "• Mobile: `{$director['mobileNo']}`\n";
			$message .= "• Email: `{$director['emailId']}`\n";

			// Show only first 5 directors for brevity
			if ($index === 20) {
				$message .= "\n...and more.";
				break;
			}
		}

		return $message;
	}

    private function formatNodalList($list)
{
    if (empty($list)) {
        return "<b>No data available.</b>";
    }

    $html = <<<HTML
<b>Nodal Contact List</b>\n
<table border="1" cellpadding="6" cellspacing="0">
  <thead>
    <tr>
      <th>#</th>
      <th>Company</th>
      <th>Name</th>
      <th>Mobile</th>
      <th>Email</th>
      <th>Address</th>
      <th>Type</th>
    </tr>
  </thead>
  <tbody>
HTML;

    foreach ($list as $index => $director) {
        $company = htmlspecialchars($director['Company']);
        $name = htmlspecialchars($director['Name_Desk_Rank']);
        $mobile = htmlspecialchars($director['ContactNumber']);
        $email = htmlspecialchars($director['EmailId']);
        $address = htmlspecialchars($director['Address']);
        $type = htmlspecialchars($director['Category']);

        $html .= "<tr>
            <td>" . ($index + 1) . "</td>
            <td>{$company}</td>
            <td>{$name}</td>
            <td>{$mobile}</td>
            <td>{$email}</td>
            <td>{$address}</td>
            <td>{$type}</td>
        </tr>";

        /*if ($index === 30) {
            $html .= '<tr><td colspan="7"><i>...and more not shown</i></td></tr>';
            break;
        }*/
    }

    $html .= "</tbody></table>";

    return $html;
}


    
    /* Company Methods */

    /* Cell ID Methods */

    public function fetchCellIdInfo($chat_id, $cgi, $lisw_token)
    {
        if (!preg_match('/^[A-Za-z0-9\-]{8,25}$/', $cgi)) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Invalid CGI format. It must be 8–25 characters.", 'Markdown');
            return;
        }

        $apiBaseUrl = $this->CI->config->item('CCASApiUrl', 'env');
        $url = $apiBaseUrl . 'GetCellExpByCGI?CGI=' . urlencode($cgi);
        $curlPost = "SortCode=AA-00test";

        $cURLConnection = curl_init($url);
        curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
        curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);

        $headers = [
            'Authorization: Bearer ' . $lisw_token,
            'Cache-Control: no-cache'
        ];
        curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);

        $result = curl_exec($cURLConnection);

        if (curl_errno($cURLConnection)) {
            $error = curl_error($cURLConnection);
            curl_close($cURLConnection);
            $this->CI->telegrambot->sendMessage($chat_id, "❌ cURL Error: `$error`", 'Markdown');
            return;
        }

        curl_close($cURLConnection);
        $response = json_decode($result);

        if (!empty($response->Data) && is_array($response->Data)) {
            $data = $response->Data[0];

            $message = "*📶 Cell ID Finder Result:*\n";
            $message .= "*CGI:* `{$data->CGI}`\n";
            $message .= "*Cell ID Code:* `{$data->CellIDCode}`\n";
            $message .= "*MCC-MNC:* `{$data->MCC}-{$data->MNC}`\n";
            $message .= "*LAC:* `{$data->LAC}`\n";
            $message .= "*Operator:* `{$data->Operator}`\n";
            $message .= "*Circle:* `{$data->Circle}`\n";

            if (!empty($data->SiteAddress)) {
                $message .= "*Site Address:* `{$data->SiteAddress}`\n";
            }

            if (!empty($data->Latitude) && !empty($data->Longitude)) {
                $message .= "*Lat:* `{$data->Latitude}`\n";
                $message .= "*Long:* `{$data->Longitude}`\n";
                $mapUrl = "https://www.google.com/maps?q={$data->Latitude},{$data->Longitude}";
                $message .= "[🗺️ View on Map]($mapUrl)\n";
            }

            $this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
        } else {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ No cell ID data found for CGI `{$cgi}`.", 'Markdown');
        }
    }

    /* Cell ID Methods */

    /* Verification Methods */

    private function fetchAadhaarInfo($chat_id, $aadhaar, $user, $lisw_token)
    {
        $date = new DateTime("now");
        $check_date = $date->format('Y-m-d');
        $user_id = $user->id;

        // Get user's Aadhaar search limits from settings
        $this->CI->load->model('SettingModel');
        $userAccountType = $user->accountType; // demo / paid
        $field = 'aadhaar_' . strtolower($userAccountType) . '_daily_credit';

        $setting = $this->CI->SettingModel->findOne([
            'name' => $field,
            'type' => 'ip_grabber'
        ]);

        if (!$setting) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Aadhaar service not configured. Please contact admin.", 'Markdown');
            return;
        }

        $userlimit = (int)$setting->value;

        $this->CI->load->model('aadhaarModel');
        $truecallerDataCount = $this->CI->aadhaarModel->count(['userType'=>'telegram','userId' => $user_id,'DATE(created_at)'=>$check_date]);

        if($truecallerDataCount >= $userlimit){
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Daily limit over. Please try tomorrow.", 'Markdown');
            return;
        }

        $this->CI->load->model('VerificationTokenModel');

        $tokenRow = $this->CI->VerificationTokenModel->getActiveToken('aadhaar');
		if (!$tokenRow) {
			$this->CI->telegrambot->sendMessage($chat_id, "❌ No active API token available.", 'Markdown');
			return;
		}
		$apiToken = $tokenRow->token;

        // ----------- FIRST API: Get PAN using Aadhaar -----------
        $firstApiPayload = json_encode([
            'aadhaar_no' => $aadhaar
        ]);

        $curl1 = curl_init();

        curl_setopt_array($curl1, array(
            CURLOPT_URL => 'https://aadhaar-to-full-pan.p.rapidapi.com/Aadhaar_to_pan',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => $firstApiPayload,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'x-rapidapi-host: aadhaar-to-full-pan.p.rapidapi.com',
                'x-rapidapi-key: ' . $apiToken
            ),
        ));

        $firstApiResponse = curl_exec($curl1);

        curl_close($curl1);

        writeLog($firstApiResponse, 'aadhaar-response');

        $firstApiData = json_decode($firstApiResponse, true);

        $pan = '';
        //$pan = '**********';
        //$pan = '**********';

        // Success case
        if (
            isset($firstApiData['status']) &&
            strtolower($firstApiData['status']) === 'success'
        ) {
            // If PAN is missing or empty
            if (!isset($firstApiData['result']['pan']) || trim($firstApiData['result']['pan']) === '') {
                $this->CI->telegrambot->sendMessage($chat_id, "❌ No Data Found", 'Markdown');
                return;
            }
            $pan = trim($firstApiData['result']['pan']);
        } else {
			$this->CI->VerificationTokenModel->setTokenStatus($tokenRow->id, 'inactive');
            $this->CI->telegrambot->sendMessage($chat_id, "❌ No Data Found", 'Markdown');
            return;
        }

        $this->CI->VerificationTokenModel->incrementTokenUsage($tokenRow->id);

        writeLog($pan, 'aadhaar-pan');

        // Do not return PAN to user, just proceed to next APIs or show generic message
        if ($pan !== '') {

            // ----------- FIRST API: LISW API -----------
            $curlPost = [
                "Din" => $pan
            ];

            $cURLConnection = curl_init('https://mobileapi.lisw.in/api/Tac/GetDinPanDataAPI');

            curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, json_encode($curlPost));
            curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);

            $headers = array();

            $headers[] = 'Authorization: Bearer ' . $lisw_token;
            $headers[] = 'Cache-Control: no-cache';
            $headers[] = 'Content-Type: application/json';

            curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);

            $result = curl_exec($cURLConnection);

            writeLog($result, 'din-pan-api-response');

            if (curl_errno($cURLConnection)) {
                $error = curl_error($cURLConnection);
                curl_close($cURLConnection);
                $this->CI->telegrambot->sendMessage($chat_id, "❌ cURL Error: `$error`", 'Markdown');
                return;
            }

            curl_close($cURLConnection);

            $response = json_decode($result);

            $dataObj = null;

            if ($response && !empty($response->data)) {
                $dataObj = json_decode($response->data, true)['data'];
            }

            if (isset($dataObj) && !empty($dataObj) ) {

                $dataInsert = $response->data;

                $message  = "*🆔 Aadhaar Information (Director):*\n";
                $message .= "*Owner Type:* `Director`\n";
                $message .= "*Nationality:* `" . (isset($dataObj['nationality']) ? $dataObj['nationality'] : 'N/A') . "`\n";
                $message .= "*Email:* `" . (isset($dataObj['emailId']) ? $dataObj['emailId'] : 'N/A') . "`\n";
                $message .= "*Mobile:* `" . (isset($dataObj['mobileNo']) ? $dataObj['mobileNo'] : 'N/A') . "`\n";
                $message .= "*Address:*\n";
                $message .= "`" . (isset($dataObj['addressLine1']) ? $dataObj['addressLine1'] : '') . "`\n";
                if (!empty($dataObj['addressLine2']) && $dataObj['addressLine2'] !== "null") {
                    $message .= "`{$dataObj['addressLine2']}`\n";
                }
                $message .= "`" . (isset($dataObj['city']) ? $dataObj['city'] : '') . ", " . (isset($dataObj['state']) ? $dataObj['state'] : '') . " - " . (isset($dataObj['pinCode']) ? $dataObj['pinCode'] : '') . "`\n";
                $message .= "*Country:* `" . (isset($dataObj['country']) ? $dataObj['country'] : 'N/A') . "`\n";

                $inserData = [
                    'userId' => $user_id,
                    'userType'=>'telegram',
                    'response' => $dataInsert,
                    'status' => 1,
                    'aadhaar' => $aadhaar,
                ];

                $this->CI->aadhaarModel->attach($inserData);

                $this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
                return;
            }

            // ----------- SECOND API: PAN Card Verification (RapidAPI) -----------

            $this->CI->load->model('VerificationTokenModel');

            // Get all active tokens for PAN service
            $tokenRows = $this->CI->VerificationTokenModel->getAllActiveTokens('pan');
            $tokenFound = false;
            $panApiData = null;
            $usedTokenRow = null;

            $status = 'FAIL';
            $dataInsert = '';

            $thirdApiPayload = json_encode([
                "PAN" => $pan
            ]);

            writeLog($thirdApiPayload, 'pan-api-payload');

            foreach ($tokenRows as $tokenRow) {

                $apiToken = $tokenRow->token;

                $curl3 = curl_init();

                curl_setopt_array($curl3, array(
                    CURLOPT_URL => 'https://pan-card-verification-at-lowest-price.p.rapidapi.com/verification/marketing/pan',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => $thirdApiPayload,
                    CURLOPT_HTTPHEADER => array(
                        'Content-Type: application/json',
                        'x-rapidapi-host: pan-card-verification-at-lowest-price.p.rapidapi.com',
                        'x-rapidapi-key: ' . $apiToken
                    ),
                ));

                $panApiResponse = curl_exec($curl3);

                curl_close($curl3);

                writeLog($panApiResponse, 'pan-api-response');

                $panApiData = json_decode($panApiResponse, true);

                if (isset($panApiData['pan']) && !empty($panApiData['pan'])) {
                    $tokenFound = true;
                    $usedTokenRow = $tokenRow;

                    $status = 1;
                    $dataInsert = json_encode($panApiData);

                    break; // Success, stop trying other tokens
                } else {
                    $this->CI->VerificationTokenModel->setTokenStatus($tokenRow->id, 'inactive');
                    // Try next token
                }
            }

            if (!$tokenFound) {
                $this->CI->telegrambot->sendMessage($chat_id, "❌ All tokens failed.", 'Markdown');
                return;
            }

            // Use the working token for the rest of the process
            $this->CI->VerificationTokenModel->incrementTokenUsage($usedTokenRow->id);

            if (isset($panApiData['pan']) && !empty($panApiData['pan'])) {

                $message  = "*🆔 Aadhaar Information (Individual):*\n";
                $message .= "*Owner Type:* `Individual`\n";
                $message .= "*Name:* `" . (isset($panApiData['name_provided']) ? $panApiData['name_provided'] : 'N/A') . "`\n";
                $message .= "*Registered Name:* `" . (isset($panApiData['registered_name']) ? $panApiData['registered_name'] : 'N/A') . "`\n";
                $message .= "*PAN Card Name:* `" . (isset($panApiData['name_pan_card']) ? $panApiData['name_pan_card'] : 'N/A') . "`\n";
                $message .= "*PAN:* `" . (isset($panApiData['pan']) ? $panApiData['pan'] : 'N/A') . "`\n";
                $message .= "*Status:* `" . (isset($panApiData['pan_status']) ? $panApiData['pan_status'] : 'N/A') . "`\n";
                $message .= "*Type:* `" . (isset($panApiData['type']) ? $panApiData['type'] : 'N/A') . "`\n";
                $message .= "*Aadhaar Status:* `" . (isset($panApiData['aadhaar_seeding_status']) ? $panApiData['aadhaar_seeding_status'] : 'N/A') . "`\n";
                $message .= "*Aadhaar Description:* `" . (isset($panApiData['aadhaar_seeding_status_desc']) ? $panApiData['aadhaar_seeding_status_desc'] : 'N/A') . "`\n";

                $inserData = [
                    'userType'=>'telegram',
                    'userId' => $user_id,
                    'response' => $dataInsert,
                    'status' => 1,
                    'aadhaar' => $aadhaar,
                ];

                $this->CI->aadhaarModel->attach($inserData);

                $this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
            } else {
                $this->CI->telegrambot->sendMessage($chat_id, "❌ No Data Found", 'Markdown');
            }

        } else {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ No Data Found", 'Markdown');
        }
    }

    public function fetchDrivingLicenseInfo($chat_id, $license, $dob, $user)
    {
		return;
        $user_id = $user->id;

        $this->CI->load->model('licenseModel');
        $this->CI->load->model('VerificationTokenModel');
        $this->CI->load->model('SettingModel');

        $date = new DateTime("now");
        $check_date = $date->format('Y-m-d');

        // Get user's license search limits from settings
        $userAccountType = $user->accountType; // demo / paid
        $field = 'dl_' . strtolower($userAccountType) . '_daily_credit';

        $setting = $this->CI->SettingModel->findOne([
            'name' => $field,
            'type' => 'ip_grabber'
        ]);

        if (!$setting) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ License service not configured. Please contact admin.", 'Markdown');
            return;
        }

        $userlimit = (int)$setting->value;

        $truecallerDataCount = $this->CI->licenseModel->count(['userType'=>'telegram', 'userId' => $user_id, 'DATE(created_at)'=>$check_date]);

        if($truecallerDataCount >= $userlimit){
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Daily limit over. Please try tomorrow.", 'Markdown');
            return;
        }

        $tokenRows = $this->CI->VerificationTokenModel->getAllActiveTokens('license');

        $tokenFound = false;
        $firstApiData = null;
        $request_id = null;
        $usedTokenRow = null;

        $payLoadDob = explode('-', $dob);
        $payLoadDob = $payLoadDob[2].'-'.$payLoadDob[1].'-'.$payLoadDob[0];

        // First API: Get request_id
        $firstApiPayload = json_encode([
            "task_id" => "74f4c926-250c-43ca-9c53-453e87ceacd1",
            "group_id" => "8e16424a-58fc-4ba4-ab20-5bc8e7c3c41e",
            "data" => [
                "id_number" => $license,
                "date_of_birth" => $payLoadDob,
                "advanced_details" => [
                    "state_info" => true,
                    "age_info" => true
                ]
            ]
        ]);

        writeLog($firstApiPayload, 'license-first-api-payload');

        foreach ($tokenRows as $tokenRow) {
            $apiToken = $tokenRow->token;

            $curl1 = curl_init();
            curl_setopt_array($curl1, array(
                CURLOPT_URL => 'https://verification-solutions.p.rapidapi.com/v3/tasks/async/verify_with_source/ind_driving_license',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => $firstApiPayload,
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json',
                    'x-rapidapi-host: verification-solutions.p.rapidapi.com',
                    'x-rapidapi-key: ' . $apiToken
                ),
            ));
            $firstApiResponse = curl_exec($curl1);
            curl_close($curl1);

            $firstApiData = json_decode($firstApiResponse, true);
            $request_id = isset($firstApiData['request_id']) ? $firstApiData['request_id'] : null;

            if ($request_id) {
                $tokenFound = true;
                $usedTokenRow = $tokenRow;
                break; // Success, stop trying other tokens
            } else {
                $this->CI->VerificationTokenModel->setTokenStatus($tokenRow->id, 'inactive');
                // Try next token
            }
        }

        if (!$tokenFound) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Unable to get request_id from License API. All tokens failed.", 'Markdown');
            return;
        }

        // Use the working token for the rest of the process
        $this->CI->VerificationTokenModel->incrementTokenUsage($usedTokenRow->id);

        // Wait before polling second API
        sleep(60);

        // Second API: Get license data using request_id
        $curl2 = curl_init();
        curl_setopt_array($curl2, array(
            CURLOPT_URL => 'https://verification-solutions.p.rapidapi.com/v3/tasks?request_id=' . $request_id,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'x-rapidapi-host: verification-solutions.p.rapidapi.com',
                'x-rapidapi-key: ' . $usedTokenRow->token
            ),
        ));
        $secondApiResponse = curl_exec($curl2);
        curl_close($curl2);

        $this->CI->VerificationTokenModel->incrementTokenUsage($usedTokenRow->id);

        $responses = json_decode($secondApiResponse, true);

        // Extract the result object
        $dataInsert = '';
        $status = 'FAIL';
        $extraction = null;
        if (is_array($responses) && isset($responses[0]['result']['source_output'])) {
            $extraction = $responses[0]['result']['source_output'];
            $dataInsert = json_encode($extraction);
            $status = isset($responses[0]['status']) && $responses[0]['status'] == 'completed' ? 1 : $responses[0]['status'];
        }

        $inserData = [
            'userId' => $user_id,
            'userType' => 'telegram',
            'response' => $dataInsert,
            'status' => $status,
            'license' => $license,
            'dob' => $dob,
        ];

        $this->CI->licenseModel->attach($inserData);

        // Format output for Telegram using the new structure
        if ($extraction) {
            $finalMessage = "*🚘 Driving License Info for:* `$license`\n\n";
            $finalMessage .= "*Name:* `" . (isset($extraction['name']) && $extraction['name'] ? $extraction['name'] : 'N/A') . "`\n";
            $finalMessage .= "*ID Number:* `" . (isset($extraction['id_number']) && $extraction['id_number'] ? $extraction['id_number'] : 'N/A') . "`\n";
            $finalMessage .= "*DL Status:* `" . (isset($extraction['dl_status']) && $extraction['dl_status'] ? $extraction['dl_status'] : 'N/A') . "`\n";
            $finalMessage .= "*D.O.B:* `" . (isset($extraction['dob']) && $extraction['dob'] ? $extraction['dob'] : 'N/A') . "`\n";
            $finalMessage .= "*RTO:* `" . (isset($extraction['issuing_rto_name']) && $extraction['issuing_rto_name'] ? $extraction['issuing_rto_name'] : 'N/A') . "`\n";
            $finalMessage .= "*Date Of Issue:* `" . (isset($extraction['date_of_issue']) && $extraction['date_of_issue'] ? $extraction['date_of_issue'] : 'N/A') . "`\n";
            $finalMessage .= "*NT Validity From:* `" . (isset($extraction['nt_validity_from']) && $extraction['nt_validity_from'] ? $extraction['nt_validity_from'] : 'N/A') . "`\n";
            $finalMessage .= "*NT Validity To:* `" . (isset($extraction['nt_validity_to']) && $extraction['nt_validity_to'] ? $extraction['nt_validity_to'] : 'N/A') . "`\n";
            $finalMessage .= "*T Validity From:* `" . (isset($extraction['t_validity_from']) && $extraction['t_validity_from'] ? $extraction['t_validity_from'] : 'N/A') . "`\n";
            $finalMessage .= "*T Validity To:* `" . (isset($extraction['t_validity_to']) && $extraction['t_validity_to'] ? $extraction['t_validity_to'] : 'N/A') . "`\n";
            $finalMessage .= "*State:* `" . (isset($extraction['state']) && $extraction['state'] ? $extraction['state'] : 'N/A') . "`\n";
            $finalMessage .= "*Is Minor:* `" . (isset($extraction['is_minor']) ? ($extraction['is_minor'] ? 'Yes' : 'No') : 'N/A') . "`\n";

            // Add COV Details if present
            if (isset($extraction['cov_details']) && is_array($extraction['cov_details'])) {
                $finalMessage .= "\n*🚗 Class of Vehicle Details:*\n";
                foreach ($extraction['cov_details'] as $cov) {
                    $finalMessage .= "*COV:* `" . (isset($cov['cov']) ? $cov['cov'] : 'N/A') . "`\n";
                    $finalMessage .= "*Category:* `" . (isset($cov['category']) ? $cov['category'] : 'N/A') . "`\n";
                    $finalMessage .= "*Issue Date:* `" . (isset($cov['issue_date']) ? $cov['issue_date'] : 'N/A') . "`\n\n";
                }
            }

            $this->CI->telegrambot->sendMessage($chat_id, $finalMessage, 'Markdown');
        } else {
            writeLog('License API extraction failed. $responses: ' . print_r($firstApiData, true) . ' $secondApiResponse: ' . print_r($responses, true), 'license-api-error');
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Some issue occurred. Please contact admin", 'Markdown');
            return;
        }
    }

    /* Verification Methods */

    /* Search Individual Methods */
	
	public function fetchVoterInfo($chat_id, $voterId, $user)
{
    $user_id = $user->id;

    $this->CI->load->model('voterModel');

    $date = new DateTime("now");
    $check_date = $date->format('Y-m-d');
    $curr_date = $date->format('Y-m-d h:i:s');

    // Get user's voter search limits from settings
    $this->CI->load->model('SettingModel');
    $userAccountType = $user->accountType; // demo / paid
    $field = 'voter_' . strtolower($userAccountType) . '_daily_credit';

    $setting = $this->CI->SettingModel->findOne([
        'name' => $field,
        'type' => 'ip_grabber'
    ]);

    if (!$setting) {
        $this->CI->telegrambot->sendMessage($chat_id, "❌ Voter service not configured. Please contact admin.", 'Markdown');
        return;
    }

    $userlimit = (int)$setting->value;

    $truecallerDataCount = $this->CI->voterModel->count(['userType' => 'telegram', 'userId' => $user_id,'DATE(created_at)'=>$check_date]);

    if($truecallerDataCount >= $userlimit){
        $this->CI->telegrambot->sendMessage($chat_id, "❌ Daily limit over. Please try tomorrow.", 'Markdown');
        return;
    }

    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://api.eitem.in/api/v1/voter/search',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => array('voter_id' => $voterId),
        CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'X-Client-ID: client_xxjIHtXeRniH6CWK',
            'X-Client-Secret: wA1i9U8izau8IfybfIeWW5hNoFY7rUtk'
        ),
    ));

    $response = curl_exec($curl);

    curl_close($curl);

    $apiData = json_decode($response, true);

    $status = 'FAIL';
    $dataInsert = '';

    if (isset($apiData['success']) && $apiData['success'] && isset($apiData['data']['details'])) {
        $details = $apiData['data']['details'];
        $dataInsert = json_encode($details);
        $status = $apiData['data']['status'] === 'success' ? 1 : 0;

        $inserData = [
            'userId' => $user_id,
            'userType' => 'telegram',
            'response' => $dataInsert,
            'status' => $status,
            'voter_number' => $voterId,
        ];

        $this->CI->voterModel->attach($inserData);

        $finalMessage = "*🗳️ Voter Card Verification Details for:* `$voterId`\n\n";
        $finalMessage .= "*Name:* `" . (isset($details['First Name']) ? $details['First Name'] : 'N/A') . " " . (isset($details['Last Name']) ? $details['Last Name'] : '') . "`\n";
        $finalMessage .= "*EPIC Number:* `" . (isset($details['EPIC No']) ? $details['EPIC No'] : 'N/A') . "`\n";
        $finalMessage .= "*Relative Name:* `" . (isset($details["Relative's First Name"]) ? $details["Relative's First Name"] : 'N/A') . " " . (isset($details["Relative's Last Name"]) ? $details["Relative's Last Name"] : '') . "`\n";
        $finalMessage .= "*Age:* `" . (isset($details['Age']) ? $details['Age'] : 'N/A') . "`\n";
        $finalMessage .= "*Gender:* `" . (isset($details['Gender']) ? $details['Gender'] : 'N/A') . "`\n";
        $finalMessage .= "*State:* `" . (isset($details['State']) ? $details['State'] : 'N/A') . "`\n";
        $finalMessage .= "*District/Assembly:* `" . (isset($details['Assembly Constituency Number-Assembly Constituency Name']) ? $details['Assembly Constituency Number-Assembly Constituency Name'] : 'N/A') . "`\n";
        $finalMessage .= "*Parliamentary Constituency:* `" . (isset($details['Parliamentary Constituency Number-Parliamentary Constituency Name']) ? $details['Parliamentary Constituency Number-Parliamentary Constituency Name'] : 'N/A') . "`\n";
        $finalMessage .= "*Part Number/Name:* `" . (isset($details['Part Number-Part Name']) ? $details['Part Number-Part Name'] : 'N/A') . "`\n";
        $finalMessage .= "*Part Serial Number:* `" . (isset($details['Part Serial Number']) ? $details['Part Serial Number'] : 'N/A') . "`\n";
        $finalMessage .= "*Polling Station:* `" . (isset($details['Polling Station']) ? $details['Polling Station'] : 'N/A') . "`\n";
        $finalMessage .= "*Polling Date:* `" . (isset($details['Polling Date']) ? $details['Polling Date'] : 'N/A') . "`\n";

        $this->CI->telegrambot->sendMessage($chat_id, $finalMessage, 'Markdown');
    } else {
        $this->CI->telegrambot->sendMessage($chat_id, "❌ No Data Found", 'Markdown');
    }
}

    private function fetchMnpInfo($chat_id, $mobile)
    {
        // Split and trim comma-separated input
        $numbers = array_map('trim', explode(',', $mobile));

        // Limit to 50
        /* if (count($numbers) > 50) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Maximum 50 mobile numbers are allowed.", 'Markdown');
            return;
        } */

        // Filter only valid 10-digit numbers
        $validNumbers = array_filter($numbers, function ($num) {
            return preg_match('/^\d{10}$/', $num);
        });

        if (empty($validNumbers)) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ No valid mobile numbers found.", 'Markdown');
            return;
        }

        $url = 'https://msg.ccas.in/api/mnp/details';

        // Prepare the string (space-separated)
        $queryString = $mobile;

        // Prepare the headers
        $headers = [
            'Authorization: Bearer ' . $this->authUser()->client_token,
        ];

        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => ['queryString' => $queryString],
            CURLOPT_HTTPHEADER => $headers,
        ]);

        $response = curl_exec($ch);

        curl_close($ch);

        $decoded = json_decode($response, true);

        if (!empty($decoded['data'])) {

            $message = "*🔁 MNP (Mobile Number Portability) Info:*\n";

            foreach ($decoded['data'] as $item) {
                $message .= "*Mobile:* `{$item['Phone']}`\n";
                $message .= "*Operator:* `{$item['Operator']}`\n";
                $message .= "*Circle:* `{$item['Circle']}`\n";
				$message .= "------------------------\n";
            }

            $this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
        } else {
            $error = 'No MNP data found.';
            $this->CI->telegrambot->sendMessage($chat_id, "❌ $error", 'Markdown');
        }
    }

    private function fetchGmailInfo($chat_id, $email, $lisw_token)
    {
        $url = 'https://mobileapi.lisw.in/api/Tac/GetGoogleAPI';

        $headers = [
            'Authorization: Bearer ' . $lisw_token,
            'Content-Type: application/json'
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['EmailId' => $email]));

        $result = curl_exec($ch);
        $http_status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if( $http_status !== 200 || !$result) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Failed to fetch Gmail info. Please try again later.", 'Markdown');
			return ;
        }

        $response = json_decode($result);

        if (!empty($response[0]->data)) {
            $data = $response[0];

            $profile = [];
            $lines = explode('||', $data->data);
            foreach ($lines as $line) {
                $parts = explode(': ', $line);
                if (count($parts) === 2) {
                    $profile[trim($parts[0])] = trim($parts[1]);
                }
            }

            $message = "*📧 Gmail Lookup Result:*\n";
            $message .= "*Email:* `$data->enterdData`\n";
            $message .= "*Status:* `$data->status`\n";

            if (isset($profile['Name'])) {
                $message .= "*Name:* `{$profile['Name']}`\n";
            }

            if (isset($profile['ID'])) {
                $message .= "*Google ID:* `{$profile['ID']}`\n";
            }

            if (isset($profile['URL'])) {
                $message .= "[🌐 View Google Profile]({$profile['URL']})\n";
            }

            if (isset($profile['Photo'])) {
                // Optional: send photo
                $this->CI->telegrambot->sendPhoto($chat_id, $profile['Photo'], "🖼️ Profile photo for `{$data->enterdData}`");
            }

            $this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
        } else {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ No Gmail data found for `$email`.", 'Markdown');
			return ;
        }
    }

	public function fetchVehicleInfo($chat_id, $vehicleNumber, $user)
	{
        $user_id = $user->id;
        $vehicle = strtoupper($vehicleNumber);

		$this->CI->load->model('vehicleModel');

		$date = new DateTime("now");
		$check_date = $date->format('Y-m-d');

		// Get user's vehicle search limits from settings
		$this->CI->load->model('SettingModel');
		$userAccountType = $user->accountType; // demo / paid
		$field = 'vehicle_' . strtolower($userAccountType) . '_daily_credit';

		$setting = $this->CI->SettingModel->findOne([
			'name' => $field,
			'type' => 'ip_grabber'
		]);

		if (!$setting) {
			$this->CI->telegrambot->sendMessage($chat_id, "❌ Vehicle service not configured. Please contact admin.", 'Markdown');
			return;
		}

		$userlimit = (int)$setting->value;

		$vehicleDataCount = $this->CI->vehicleModel->count(['userType'=>'telegram','userId' => $user_id,'DATE(created_at)'=>$check_date]);

		if($vehicleDataCount >= $userlimit){
			$this->CI->telegrambot->sendMessage($chat_id, "❌ Daily limit over. Please try tomorrow.", 'Markdown');
			return;
		}

		$this->CI->load->model('VerificationTokenModel');

		// Get active token for vehicle service
		$tokenRow = $this->CI->VerificationTokenModel->getActiveToken('vehicle');
		if (!$tokenRow) {
			$this->CI->telegrambot->sendMessage($chat_id, "❌ No active API token available.", 'Markdown');
			return;
		}

		$apiToken1 = $tokenRow->token; // For first API
		$apiToken2 = $tokenRow->token; // For second API (if you use same token)

		// First API: Get request_id
		$firstApiPayload = json_encode([
			"task_id" => "74f4c926-250c-43ca-9c53-453e87ceacd1",
			"group_id" => "8e16424a-58fc-4ba4-ab20-5bc8e7c3c41e",
			"data" => [
				"rc_number" => $vehicle,
				"chassis_number" => "<Chassis Number>"
			]
		]);

		writeLog($firstApiPayload, 'vehicle-first-api-payload');

		$curl1 = curl_init();
		curl_setopt_array($curl1, array(
			CURLOPT_URL => 'https://verification-solutions.p.rapidapi.com/v3/tasks/async/verify_with_source/ind_rc_plus',
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS => $firstApiPayload,
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json',
				'x-rapidapi-host: verification-solutions.p.rapidapi.com',
				'x-rapidapi-key: ' . $apiToken1
			),
		));

		$firstApiResponse = curl_exec($curl1);

		curl_close($curl1);

		$firstApiData = json_decode($firstApiResponse, true);

		$request_id = isset($firstApiData['request_id']) ? $firstApiData['request_id'] : null;

		if (!$request_id) {
			// Optionally mark token as expired if API says so
			$this->CI->VerificationTokenModel->setTokenStatus($tokenRow->id, 'inactive');
			$this->CI->telegrambot->sendMessage($chat_id, "❌ Unable to get request_id from RC API.", 'Markdown');
			return;
		}

		// Increment usage after first API call
		$this->CI->VerificationTokenModel->incrementTokenUsage($tokenRow->id);

		// Wait before polling second API
		sleep(10);

		// Second API: Get vehicle data using request_id
		$curl2 = curl_init();
		curl_setopt_array($curl2, array(
			CURLOPT_URL => 'https://verification-solutions.p.rapidapi.com/v3/tasks?request_id=' . $request_id,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'GET',
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json',
				'x-rapidapi-host: verification-solutions.p.rapidapi.com',
				'x-rapidapi-key: ' . $apiToken2
			),
		));
		$secondApiResponse = curl_exec($curl2);
		curl_close($curl2);

		// Increment usage after second API call (optional, if you want to count both)
		$this->CI->VerificationTokenModel->incrementTokenUsage($tokenRow->id);

		$responses = json_decode($secondApiResponse, true);

		// Extract the result object
		$dataInsert = '';
		$status = 'FAIL';
		$extraction = null;
		if (is_array($responses) && isset($responses[0]['result']['extraction_output'])) {
			$extraction = $responses[0]['result']['extraction_output'];
			$dataInsert = json_encode($extraction);
			$status = $responses[0]['status'] && $responses[0]['status'] == 'completed' ? 1 : $responses[0]['status'];
		}

		$inserData = [
			'userId' => $user_id,
			'userType' => 'telegram',
			'response' => $dataInsert,
			'status' => $status,
			'vehicle' => $vehicle,
		];

		$this->CI->vehicleModel->attach($inserData);

		// Format output for Telegram using the new structure
		if ($extraction) {
			// 📌 Format Personal Information
			$finalMessage = "*🚘 Vehicle Details for:* `$vehicleNumber`\n\n";
			$finalMessage .= "*👤 Personal Information*\n";
			$finalMessage .= "*Registration Number:* `" . (isset($extraction['registration_number']) && $extraction['registration_number'] ? $extraction['registration_number'] : 'N/A') . "`\n";
			$finalMessage .= "*Owner Name:* `" . (isset($extraction['owner_name']) && $extraction['owner_name'] ? $extraction['owner_name'] : 'N/A') . "`\n";
			$finalMessage .= "*Owner No:* `" . (isset($extraction['owner_mobile_no']) && $extraction['owner_mobile_no'] ? $extraction['owner_mobile_no'] : 'N/A') . "`\n";
			$finalMessage .= "*Father's Name:* `" . (isset($extraction['father_name']) && $extraction['father_name'] ? $extraction['father_name'] : 'N/A') . "`\n";
			$finalMessage .= "*Permanent Address:* `" . (isset($extraction['permanent_address']) && $extraction['permanent_address'] ? $extraction['permanent_address'] : 'N/A') . "`\n";
			$finalMessage .= "*Current Address:* `" . (isset($extraction['current_address']) && $extraction['current_address'] ? $extraction['current_address'] : 'N/A') . "`\n";
			$finalMessage .= "*Owner Serial Number:* `" . (isset($extraction['owner_serial_number']) && $extraction['owner_serial_number'] ? $extraction['owner_serial_number'] : 'N/A') . "`\n";
			$finalMessage .= "*Date Of Registration:* `" . (isset($extraction['registration_date']) && $extraction['registration_date'] ? $extraction['registration_date'] : 'N/A') . "`\n";
			$finalMessage .= "*State:* `" . (isset($extraction['state']) && $extraction['state'] ? $extraction['state'] : 'N/A') . "`\n";

			// 📌 Format Vehicle Information
			$finalMessage .= "\n*🚗 Vehicle Information*\n";
			$finalMessage .= "*Manufacturer Model:* `" . (isset($extraction['manufacturer_model']) && $extraction['manufacturer_model'] ? $extraction['manufacturer_model'] : 'N/A') . "`\n";
			$finalMessage .= "*Manufacturer:* `" . (isset($extraction['manufacturer']) && $extraction['manufacturer'] ? $extraction['manufacturer'] : 'N/A') . "`\n";
			$finalMessage .= "*Manufacturing Year:* `" . (isset($extraction['m_y_manufacturing']) && $extraction['m_y_manufacturing'] ? $extraction['m_y_manufacturing'] : 'N/A') . "`\n";
			$finalMessage .= "*Vehicle's Class:* `" . (isset($extraction['vehicle_class']) && $extraction['vehicle_class'] ? $extraction['vehicle_class'] : 'N/A') . "`\n";
			$finalMessage .= "*Color:* `" . (isset($extraction['colour']) && $extraction['colour'] ? $extraction['colour'] : 'N/A') . "`\n";
			$finalMessage .= "*Number Of Cylinder:* `" . (isset($extraction['number_of_cylinder']) && $extraction['number_of_cylinder'] ? $extraction['number_of_cylinder'] : 'N/A') . "`\n";
			$finalMessage .= "*Fuel Type:* `" . (isset($extraction['fuel_type']) && $extraction['fuel_type'] ? $extraction['fuel_type'] : 'N/A') . "`\n";
			$finalMessage .= "*Seating Capacity:* `" . (isset($extraction['seating_capacity']) && $extraction['seating_capacity'] ? $extraction['seating_capacity'] : 'N/A') . "`\n";
			$finalMessage .= "*Registered Place:* `" . (isset($extraction['registered_place']) && $extraction['registered_place'] ? $extraction['registered_place'] : 'N/A') . "`\n";
			$finalMessage .= "*Chassis Number:* `" . (isset($extraction['chassis_number']) && $extraction['chassis_number'] ? $extraction['chassis_number'] : 'N/A') . "`\n";
			$finalMessage .= "*Engine Number:* `" . (isset($extraction['engine_number']) && $extraction['engine_number'] ? $extraction['engine_number'] : 'N/A') . "`\n";

			// 📌 Format Insurance Details
			$finalMessage .= "\n*🛡️ Insurance Details*\n";
			$finalMessage .= "*Insurance Name:* `" . (isset($extraction['insurance_name']) && $extraction['insurance_name'] ? $extraction['insurance_name'] : 'N/A') . "`\n";
			$finalMessage .= "*Insurance Policy Number:* `" . (isset($extraction['insurance_policy_no']) && $extraction['insurance_policy_no'] ? $extraction['insurance_policy_no'] : 'N/A') . "`\n";
			$finalMessage .= "*Insurance Validity:* `" . (isset($extraction['insurance_validity']) && $extraction['insurance_validity'] ? $extraction['insurance_validity'] : 'N/A') . "`\n";
			$finalMessage .= "*Financer:* `" . (isset($extraction['financer']) && $extraction['financer'] ? $extraction['financer'] : 'N/A') . "`\n";
			$finalMessage .= "*PUC Number:* `" . (isset($extraction['puc_number']) && $extraction['puc_number'] ? $extraction['puc_number'] : 'N/A') . "`\n";
			$finalMessage .= "*Fitness Upto:* `" . (isset($extraction['fitness_upto']) && $extraction['fitness_upto'] ? $extraction['fitness_upto'] : 'N/A') . "`\n";
			$finalMessage .= "*PUC Valid Upto:* `" . (isset($extraction['puc_valid_upto']) && $extraction['puc_valid_upto'] ? $extraction['puc_valid_upto'] : 'N/A') . "`\n";

			$this->CI->telegrambot->sendMessage($chat_id, $finalMessage, 'Markdown');
		} else {
			// Log the error details for debugging
            writeLog('Vehicle API extraction failed. $responses: ' . print_r($responses, true) . ' $secondApiResponse: ' . $secondApiResponse, 'vehicle-error');
			$this->CI->telegrambot->sendMessage($chat_id, "❌ Some issue occurred. Please contact admin", 'Markdown');
		}
	}

    public function fetchWhatsappInfo($chat_id, $mobile, $user)
    {
        $user_id = $user->id;

        $curl = curl_init();
			curl_setopt_array($curl, array(
				CURLOPT_URL => 'https://api.eitem.in/api/v1/whatsapp/check',
				CURLOPT_RETURNTRANSFER => true,
				CURLOPT_ENCODING => '',
				CURLOPT_MAXREDIRS => 10,
				//CURLOPT_TIMEOUT => 2,
				CURLOPT_FOLLOWLOCATION => true,
				CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				CURLOPT_CUSTOMREQUEST => 'POST',
				CURLOPT_POSTFIELDS => http_build_query(['mobile_number' => $mobile]),
				CURLOPT_HTTPHEADER => array(
					'Accept: application/json',
					'X-Client-ID: client_xxjIHtXeRniH6CWK',
					'X-Client-Secret: wA1i9U8izau8IfybfIeWW5hNoFY7rUtk'
				),
			));

        $response = curl_exec($curl);
		
		if (curl_errno($curl)) {
            writelog(curl_error($curl), 'wh_error');
        }

        curl_close($curl);

        $decoded = json_decode($response);

        writelog($decoded, 'whatsapp-tg');

        if (isset($decoded->success) && $decoded->success === true) {

            $data = $decoded->data;

            $name    = !empty($data->name) ? $data->name : 'N/A';
            $accountType = !empty($data->account_type) ? $data->account_type : 'N/A';
            $registrationStatus = !empty($data->registration_status) ? $data->registration_status : 'N/A';
            $status  = !empty($data->status) ? $data->status : 'N/A';
            $mobile  = !empty($data->phone_number) ? $data->phone_number : 'N/A';

            $message = "*📱 WhatsApp Profile Info:*\n";
            $message .= "*Name:* `{$name}`\n";
            $message .= "*Account Type:* `{$accountType}`\n";
            $message .= "*Mobile:* `{$mobile}`\n";
            $message .= "*Status:* `{$status}`\n";
            $message .= "*Registration Status:* `{$registrationStatus}`\n";

            // Send profile photo if available
            if (!empty($data->profile_photo)) {
                $this->CI->telegrambot->sendPhoto($chat_id, $data->profile_photo, "🖼️ WhatsApp Profile Picture for `$mobile`");
            }

            $this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
			return ;
        } else {
            $this->CI->telegrambot->sendMessage($chat_id, "⚠️ Something went wrong. Please contact support.", 'Markdown');
			return ;
        }        
    }

	public function handleProxyCheck($chat_id, $ipAddress, $lisw_token)
	{
		$headers = [
			'Authorization: Bearer ' . $lisw_token,
			'Content-Type: application/json'
		];

		$payload = json_encode([
			"SearchIpaddress" => [$ipAddress]
		]);

		$ch = curl_init('https://mobileapi.lisw.in/api/Tac/GetProxyIPData');
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);

		$result = curl_exec($ch);
		curl_close($ch);

		$decoded = json_decode($result);

		if (!empty($decoded->Data)) {
			$data = $decoded->Data[0];

			$message = "*🌐 Proxy Check Result:*\n";
			$message .= "*IP:* `{$ipAddress}`\n";
			$message .= "*Proxy Type:* `{$data->ProxyType}`\n";
			$message .= "*Country:* `{$data->Country}`\n";
			$message .= "*Region:* `{$data->Region}`\n";
			$message .= "*City:* `{$data->City}`\n";
			$message .= "*ISP:* `{$data->ISP}`\n";
			$message .= "*Domain:* `{$data->Domain}`\n";
			$message .= "*Usage Type:* `{$data->UsageType}`\n";
			$message .= "*ASN:* `{$data->ASN}`\n";
			$message .= "*Last Seen:* `{$data->LastSeen}`\n";
			$message .= "*Threat:* `{$data->Threat}`\n";
			$message .= "*Residential:* `{$data->Residential}`\n";
			$message .= "*Provider:* `{$data->Provider}`\n";

			$this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
		} else {
			$this->CI->telegrambot->sendMessage($chat_id, "❌ No proxy data found for `$ipAddress`.", 'Markdown');
		}
	}

	public function fetchIPInfo($chat_id, $ipAddress, $lisw_token)
	{
		$headers = [
			'Authorization: Bearer ' . $lisw_token,
			'Content-Type: application/json'
		];

		$payload = json_encode([
			"SearchIpaddress" => [$ipAddress]
		]);

		$ch = curl_init('https://mobileapi.lisw.in/api/Tac/GetWebsiteByDestinationIP');
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);

		$result = curl_exec($ch);

		curl_close($ch);

		$decoded = json_decode($result);

		if (!empty($decoded->Data)) {
			$data = $decoded->Data[0];

			$message = "*🌐 IP Result:*\n";
			$message .= "*IP:* `{$ipAddress}`\n";
			$message .= "*Country:* `{$data->CountryName}`\n";
			$message .= "*ISP:* `{$data->WebsiteName}`\n";
			$message .= "*Org:* `{$data->CompanyDescription}`\n";
			$message .= "*Type:* `{$data->Type}`\n";

			$this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
		} else {
			$this->CI->telegrambot->sendMessage($chat_id, "❌ No IP data found for `$ipAddress`.", 'Markdown');
		}
	}

    private function fetchImeiInfo($chat_id, $imei, $lisw_token)
    {
		
		$shortImei = substr($imei, 0, 8);

        $url = 'https://mobileapi.lisw.in/api/Tac/GetCompanyName';
        $postBody = json_encode(["IMEI" => $shortImei]);

        $headers = [
            'Authorization: Bearer ' . $lisw_token,
            'Content-Type: application/json',
            'Cache-Control: no-cache'
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postBody);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);

        curl_close($ch);

        $decoded = json_decode($response);

        if (isset($decoded->StatusCode) && $decoded->StatusCode == 1 && !empty($decoded->Data)) {
            $data = $decoded->Data[0];
            $company = $data->CompanyName ? $data->CompanyName : 'N/A';
            $model = isset($data->Model) ? $data->Model : 'N/A';
            $tac = isset($data->TAC) ? $data->TAC : 'N/A';

            $message = "*📱 IMEI Lookup Result:*\n";
            $message .= "*IMEI:* `$imei`\n";
            $message .= "*Company:* `$company`\n";
            $message .= "*Model:* `$model`\n";
            $message .= "*TAC:* `$tac`\n";

            $this->CI->telegrambot->sendMessage($chat_id, $message, 'Markdown');
        } else {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ IMEI `$imei` not found or invalid.", 'Markdown');
        }
    }

    public function fetchSecondImeiInfo($chat_id, $imei, $lisw_token)
    {
        $imeiList = [];

        // Primary IMEI
        $primaryImei = $imei . imeiFifteenthDigit($imei);
        $imeiList['Primary IMEI'] = $primaryImei;

        // +1 IMEI
        $plus1 = $imei + 1;
        $plus1Full = $plus1 . imeiFifteenthDigit($plus1);
        $imeiList['Option +1'] = $plus1Full;

        // -1 IMEI
        $minus1 = $imei - 1;
        $minus1Full = $minus1 . imeiFifteenthDigit($minus1);
        $imeiList['Option -1'] = $minus1Full;

        // TAC +1
        $str1 = substr($imei, 0, 6);
        $str2 = substr($imei, 6);
        $tacPlus = ($str1 + 1) . $str2;
        $tacPlusFull = $tacPlus . imeiFifteenthDigit($tacPlus);
        $imeiList['TAC +1'] = $tacPlusFull;

        // TAC -1
        $tacMinus = ($str1 - 1) . $str2;
        $tacMinusFull = $tacMinus . imeiFifteenthDigit($tacMinus);
        $imeiList['TAC -1'] = $tacMinusFull;

        $msg = "*📲 IMEI Variants & Brands Based on:* `$imei`\n";

        // Get company name for each variant
        foreach ($imeiList as $label => $imeiVal) {
            $tac = substr($imeiVal, 0, 8);
            $imeiData = companyDetails($tac, $lisw_token);

            $company = (!empty($imeiData->Data[0]->CompanyName) && $imeiData->StatusCode == 1)
                ? $imeiData->Data[0]->CompanyName
                : 'N/A';

            $msg .= "\n*$label:*\n`$imeiVal`\n*Company:* `$company`\n";
        }

        $this->CI->telegrambot->sendMessage($chat_id, $msg, 'Markdown');
    }

    public function fetchTruecallerInfo($chat_id, $mobile, $lisw_token, $user_id)
    {
        $url = 'https://mobileapi.lisw.in/api/Tac/GetTrucallerAPI';

        $param = json_encode(['Moblie' => $mobile]);

        $headers = [
            'Authorization: Bearer ' . $lisw_token,
            'Content-Type: application/json'
        ];

        $ch = curl_init($url);
		
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		
        curl_setopt($ch, CURLOPT_POSTFIELDS, $param);
		
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		
        $result = curl_exec($ch);
		
		if (curl_errno($ch)) {
            writelog(curl_error($ch), 'truecaller_error');
        }

        curl_close($ch);

        if (!$result) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Error connecting to Truecaller API.", 'Markdown');
            return;
        }

        $response = json_decode($result, true);

		if (empty($response['success']) || empty($response['data'])) {
			$msg = !empty($response['message']) ? $response['message'] : 'No result found.';
			$this->CI->telegrambot->sendMessage($chat_id, "❌ $msg", 'Markdown');
			return;
		}
			
		$truecaller_data = json_decode($response['data']);
		
		$value = isset($truecaller_data[0]->value) ? $truecaller_data[0]->value : $truecaller_data[0];

		$name     = isset($value->name) ? $value->name : 'N/A';
		$gender   = !empty($value->gender) ? $value->gender : 'N/A';
		$image    = !empty($value->image) ? $value->image : '';
		$altName  = !empty($value->altName) ? $value->altName : 'N/A';
		$about    = !empty($value->about) ? $value->about : 'N/A';
        $email    = !empty($value->internetAddresses) ? $value->internetAddresses[0]->id : 'N/A';
        $country  = !empty($value->addresses[0]->address) ? $value->addresses[0]->address : 'N/A';
        $State     = !empty($value->addresses[0]->city) ? $value->addresses[0]->city : 'N/A';
        $carrier  = !empty($value->phones[0]->carrier) ? $value->phones[0]->carrier : 'N/A';
        $nationalFormat = !empty($value->phones[0]->nationalFormat) ? $value->phones[0]->nationalFormat : 'N/A';
		$spamInfo = !empty($value->spamInfo) ? $value->spamInfo : [];
        $phones = !empty($value->phones) ? $value->phones : [];
        $addresses = !empty($value->addresses) ? $value->addresses : [];
        $internetAddresses = !empty($value->internetAddresses) ? $value->internetAddresses : [];
		
		$msg  = "*📞 Truecaller Lookup Result:*\n";
		$msg .= "*Mobile:* `$mobile`\n";
		$msg .= "*Name:* `$name`\n";
		$msg .= "*Phone:* `$nationalFormat`\n";
		$msg .= "*Carrier:* `$carrier`\n";
		$msg .= "*Email:* `$email`\n";
		$msg .= "*Gender:* `$gender`\n";
		$msg .= "*Alt Name:* `$altName`\n";
		$msg .= "*About:* `$about`\n";
		$msg .= "*Country:* `$country`\n";
		$msg .= "*State:* `$State`\n";

		if (!empty($spamInfo)) {
			$msg .= "\n*🚫 Spam Info:*\n";
			$msg .= "*Number of Reports:* `" . (!empty($spamInfo->spamStats->numReports) ? $spamInfo->spamStats->numReports : 'N/A') . "`\n";
			$msg .= "*Reports in Last 60 Days:* `" . (!empty($spamInfo->spamStats->numReports60days) ? $spamInfo->spamStats->numReports60days : 'N/A') . "`\n";
			$msg .= "*Searches in Last 60 Days:* `" . (!empty($spamInfo->spamStats->numSearches60days) ? $spamInfo->spamStats->numSearches60days : 'N/A') . "`\n";
			$msg .= "*Calls in Last 60 Days:* `" . (!empty($spamInfo->spamStats->numCalls60days) ? $spamInfo->spamStats->numCalls60days : 'N/A') . "`\n";
			$msg .= "*Calls Not Answered:* `" . (!empty($spamInfo->spamStats->numCallsNotAnswered) ? $spamInfo->spamStats->numCallsNotAnswered : 'N/A') . "`\n";
			$msg .= "*Calls Answered:* `" . (!empty($spamInfo->spamStats->numCallsAnswered) ? $spamInfo->spamStats->numCallsAnswered : 'N/A') . "`\n";
			$msg .= "*Messages in Last 60 Days:* `" . (!empty($spamInfo->spamStats->numMessages60days) ? $spamInfo->spamStats->numMessages60days : 'N/A') . "`\n";

			if (!empty($spamInfo->spamStats->topSpammedCountries)) {
				$msg .= "\n*🌍 Top Spammed Countries:*\n";
				foreach ($spamInfo->spamStats->topSpammedCountries as $country) {
					$msg .= "• *Country Code:* `$country->countryCode` — *Calls:* `$country->numCalls`\n";
				}
			} else {
				$msg .= "*Top Spammed Countries:* `No data available`\n";
			}
		} else {
			$msg .= "\n*🚫 Spam Info:* `No spam info available`\n";
		}


        $this->CI->load->model('truecallerModel');

        $this->CI->truecallerModel->attach([
            'userType' => 'telegram',
            'userId' => $user_id,
            'mobile' => $mobile,
            'name' => $name,
            'gender' => $gender,
            'image' => $image,
            'altName' => $altName,
            'about' => $about,
            'phone' => (string)json_encode($phones),
            'address' => (string)json_encode($addresses),
            'internetAddress' => (string)json_encode($internetAddresses),
            'spam_description' => (string)json_encode($spamInfo),
        ]);
				
		if (!empty($image)) {
			$this->CI->telegrambot->sendPhoto($chat_id, $image, "🖼️ Profile image for `$mobile`");
		}

        $this->CI->TelegramModel->incrementOrDecrement('truecallerSearchCredit', -1, ['id' => $user_id]);

		$this->CI->telegrambot->sendMessage($chat_id, $msg, 'Markdown');    
    }

    public function fetchOsintData($chat_id, $input, $user)
    {
		die();
		//exit;
        $isEmail = filter_var($input, FILTER_VALIDATE_EMAIL);
        $params = [
            'userId'  => $user ? $user->id : 1,
            'email'   => $isEmail ? $input : '',
            'mobile'  => !$isEmail ? $input : '',
            'uname' => '9610505499',
            'password' => '123456',
        ];

        try {
			
            $this->CI->load->library('OsintServices');
			
            $result = $this->CI->osintservices->fetchData($params);

            if (!$result || !is_array($result)) {
                $this->CI->telegrambot->sendMessage($chat_id, "❌ No OSINT data found for `$input`.");
                return;
            }

            //$records = $result['bulk']['data'] ? $result['bulk']['data'] : [];

            //$records = testData();

            $html = generateOsintPdfHtml($result, $input);

            $this->CI->load->library('pdf');

            $fileName = "osint_report_" . time() . ".pdf";
            $filePath = FCPATH . "uploads/telegram_osint/" . $fileName;
            $publicUrl = base_url("uploads/telegram_osint/" . $fileName);
			
			$this->CI->pdf->telegramLink($html, $filePath);

            $inputData['userType'] = 'telegram';
            $inputData['userId'] = $user->id;
            $inputData['username'] = $input;
            $inputData['status'] = 'success';

            $osintId = $this->CI->osintModel->attach($inputData);

            $this->CI->TelegramModel->incrementOrDecrement('osintSearchCredit', -1, ['id' => $user->id]);

            // Send link to user
            $msg = "📄 *Your OSINT PDF report is ready:*\n\n[Download PDF]($publicUrl)";
            $this->CI->telegrambot->sendMessage($chat_id, $msg, 'Markdown');
			exit;
        } catch (Exception $e) {
            $this->CI->telegrambot->sendMessage($chat_id, "⚠️ Error during OSINT lookup: " . $e->getMessage());
			exit;
        }
    }

	public function fetchMobileSDRInfo($chat_id, $mobile)
	{
		$param = json_encode([
			"mobile" => $mobile,
			"id"     => 1
		]);

		$headers = [
			'Content-Type: application/json',
			'Authorization: ma7641e1064c36f27fa1fd802ea436av5b0a553ead9f97f39de75f2f51b25a983236nish'
		];

		$ch = curl_init("https://eitem.in/vehicle/fetch-mobile.php");

		curl_setopt_array($ch, [
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_POSTFIELDS     => $param,
			CURLOPT_HTTPHEADER     => $headers,
			CURLOPT_SSL_VERIFYPEER => false,
			CURLOPT_SSL_VERIFYHOST => false,
			CURLOPT_CUSTOMREQUEST  => 'POST',
		]);

        /* $this->CI->telegrambot->sendMessage($chat_id, "Please wait, we're retrieving the information", 'Markdown'); */

		$response = curl_exec($ch);

		curl_close($ch);

		$decoded = json_decode($response);

		if (isset($decoded->response) && $decoded->status) {
			$data = json_decode($decoded->response, true);
			$info = $data[0]['result']['source_output'];

			$msg = "*📱 Mobile SDR Details:*\n";
			$msg .= "*Mobile:* `$mobile`\n";
			$msg .= "*Name:* `" . (!empty($info['mobile_number_details']['name']) ? $info['mobile_number_details']['name'] : 'N/A') . "`\n";
			$msg .= "*Status:* `" . (!empty($info['mobile_number_details']['mobile_number_status']) ? $info['mobile_number_details']['mobile_number_status'] : 'N/A') . "`\n";
			$msg .= "*Connection Type:* `" . (!empty($info['mobile_number_details']['mobile_connection_type']) ? $info['mobile_number_details']['mobile_connection_type'] : 'N/A') . "`\n";
			$msg .= "*Is Ported:* `" . (!empty($info['mobile_number_details']['is_ported']) ? $info['mobile_number_details']['is_ported'] : 'N/A') . "`\n";
			$msg .= "*Porting History:* `" . (!empty($info['mobile_number_details']['porting_history']) ? $info['mobile_number_details']['porting_history'] : 'N/A') . "`\n";
			$msg .= "*Alternate Number:* `" . (!empty($info['mobile_number_details']['alternate_number']) ? $info['mobile_number_details']['alternate_number'] : 'N/A') . "`\n";
			$msg .= "*MSISDN:* `" . (!empty($info['msisdn_details']['msisdn']) ? $info['msisdn_details']['msisdn'] : 'N/A') . "`\n";
			$msg .= "*IMSI:* `" . (!empty($info['msisdn_details']['imsi']) ? $info['msisdn_details']['imsi'] : 'N/A') . "`\n";
			$msg .= "*Network:* `" . (!empty($info['current_service_provider']['network_name']) ? $info['current_service_provider']['network_name'] : 'N/A') . "`\n";
			$msg .= "*Network Region:* `" . (!empty($info['current_service_provider']['network_region']) ? $info['current_service_provider']['network_region'] : 'N/A') . "`\n";
			$msg .= "*Status Code:* `" . (!empty($info['connection_status']['status_code']) ? $info['connection_status']['status_code'] : 'N/A') . "`\n";

			$this->CI->telegrambot->sendMessage($chat_id, $msg, 'Markdown');
		} else {
			$this->CI->telegrambot->sendMessage($chat_id, "❌ Mobile SDR lookup failed. Please try again later.", 'Markdown');
			return ;
		}
	}
    /* Search Individual Methods */

    /* IP Grabber */

    public function fetchLinkCreation($chat_id, $telegram_id, $input)
    {
        // Step 1: Extract input pattern
        // Format: "FIR/Case Name | Redirect:https://example.com | Cam=yes | Location=yes | Paste=No"
        $parts = explode('|', $input);

        //writelog($parts, 'link_creation');

        $caseName = trim($parts[0]);

        $params = [
            'redirectUrl' => null,
            'description' => '',
            'isCamera' => 'yes',
            'isPaste' => 'no',
            'location' => 'yes'
        ];

        foreach ($parts as $part) {
            $part = trim($part);
            if (stripos($part, 'redirect:') !== false) {
				$url = trim(str_ireplace('redirect:', '', $part));
				// Add https:// if missing
				if (!preg_match('~^https?://~i', $url)) {
					$url = 'https://' . $url;
				}
				$params['redirectUrl'] = $url;
            } elseif (stripos($part, 'desc=') !== false) {
                $params['description'] = trim(str_ireplace('desc=', '', $part));
            } elseif (stripos($part, 'cam=') !== false) {
                $params['isCamera'] = trim(str_ireplace('cam=', '', $part));
            } elseif (stripos($part, 'paste=') !== false) {
                $params['isPaste'] = trim(str_ireplace('paste=', '', $part));
            } elseif (stripos($part, 'location=') !== false) {
                $params['location'] = trim(str_ireplace('location=', '', $part));
            }
        }

        if (strlen($caseName) < 2 || strlen($caseName) > 40) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Case name must be between 2 and 40 characters.");
            return;
        }

        if(empty($params['redirectUrl'])) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Redirect Url can not be empty.");
            return;
        }

        $user = $this->CI->TelegramModel->findOne(['telegramId' => $telegram_id]);

        $userData = [
            'userId' => $user->id,
            'victimNameOrFir' => $caseName,
            'description' => $params['description'],
            'location' => $params['location'] == 'yes' ? 0 : 1,
            'isCamera' => $params['isCamera'] == 'yes' ? 1 : 0,
            'isPaste' => $params['isPaste'] == 'yes' ? 1 : 0,
			'userType' => 'telegram',
            'createdOn' => currentDateTime(),
            'isStatus' => 'active',
        ];

        // Step 2: Handle redirect
        if (!empty($params['redirectUrl'])) {
            $userData["redirectUrl"] = $params['redirectUrl'];
            $userData["isRedirect"] = 'yes';
            $header = getHeader($params["redirectUrl"]);
            $userData["title"] = $header['title'] ? $header['title'] : $caseName; // Use case name if title is empty
            $userData["description"] = htmlspecialchars($header['description'] ? $header['description'] : $params['description']);
            $userData["favicon"] = $header['favicon'] ? $header['favicon'] : '';
        } else {
            $userData["isRedirect"] = 'no';
        }

        // Step 3: Save to DB
        $this->CI->load->model('linkModel');
        $insertId = $this->CI->linkModel->attach($userData);
		//$insertId = 24786;

        if ($insertId) {
            $linkData = $this->CI->linkModel->findOne(['id' => $insertId]);
            $linkUrl = TRACK_URL . "goo/" . base64_encode($linkData->id); // Adjust this to your route logic

            $msg = "*🔗 Link Created:*\n";
            $msg .= "*Title:* {$caseName}\n";
            $msg .= "*Link:* {$linkUrl}\n";
            //if (!empty($linkData->description)) $msg .= "*Description:* {$linkData->description}\n";
            //if (!empty($linkData->redirectUrl)) $msg .= "*Redirect:* {$linkData->redirectUrl}\n";
            //$msg .= "*Camera:* {$linkData->isCamera}, *Location:* {$linkData->location}, *Paste:* {$linkData->isPaste}\n";
            $msg .= "\n🔗 *Access Link:* [Click Here]($linkUrl)";

            $this->CI->telegrambot->sendMessage($chat_id, $msg, 'Markdown');

            $keyboard = [
                'inline_keyboard' => [[
                    ['text' => 'G00Gl.CO.IN', 'callback_data' => "customize_{$insertId}_https://www.G00Gl.CO.IN"],
                    ['text' => 'pornhubs.in', 'callback_data' => "customize_{$insertId}_https://www.pornhubs.in"],
                ],[
                    ['text' => 'yuotube.in', 'callback_data' => "customize_{$insertId}_https://www.yuotube.in"],
                    ['text' => 'fa.cebook.In', 'callback_data' => "customize_{$insertId}_https://fa.cebook.In"],
                ],[
                    ['text' => 'p.aytm.in', 'callback_data' => "customize_{$insertId}_https://p.aytm.in"]
                ]]
            ];

            $this->CI->telegrambot->sendMessageWithKeyboard($chat_id, "🌐 *Choose a domain to generate your customized link:*", $keyboard);
        } else {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Failed to create link. Please try again.");
        }
    }

    public function customizeLink($chat_id, $linkId, $domain)
    {
        $encoded = base64_encode($linkId);
        $customLink = rtrim($domain, '/') . '/goo/' . $encoded;

        $this->CI->load->model('linkModel');

        $updated = $this->CI->linkModel->modify(['customizeLink' => $customLink], ['id' => $linkId]);

        if ($updated) {
            $msg .= "✅ *Customized Link Created:*\n";
            $msg .= "*Link:* {$customLink}\n";
            $msg .= "\n🔗 *Access Link:* [Click Here]($customLink)";
            $this->CI->telegrambot->sendMessage($chat_id, $msg, 'Markdown');
        } else {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Failed to update customized link.");
        }
    }

    private function viewLinks($chat_id, $telegram_id)
    {
        $this->CI->load->model('linkModel');

        $user = $this->CI->TelegramModel->findOne(['telegramId' => $telegram_id]);

        if (!isObject($user)) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ User not found.");
            return;
        }

        $links = $this->CI->linkModel->find(['userId' => $user->id, 'userType' => 'telegram'], '*', ['orderBy' => ['createdOn', "DESC"],
            "limit" => ['perPage' => 50, 'offset' => 0]]);

        if (empty($links)) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ No links found for your account.");
            return;
        }

        $msg = "*🔗 Your Links:*\n";
        foreach ($links as $link) {
            $linkId = base64_encode($link->id);

            $linkUrl = TRACK_URL . "goo/" . base64_encode($link->id); // Adjust this to your route logic
            $msg .= "*Title:* {$link->victimNameOrFir}\n";
            $msg .= "*Link:* {$linkUrl}\n";
            $msg .= "\n🔗 *Access Link:* [Click Here]($linkUrl)\n";

            $msg .= "* Action:*\n";
            $msg .= "[View Details] /view_link{$linkId}\n";
            $msg .= "[Delete Link] /delete_link{$linkId}\n";
            $msg .= "------------------------\n";
        }
		
		$this->CI->telegrambot->sendMessage($chat_id, $msg, 'Markdown');
    }

    private function viewLinkDetails($chat_id, $linkId, $telegram_id)
    {

        $user = $this->CI->TelegramModel->findOne(['telegramId' => $telegram_id]);

        if (!isObject($user)) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ User not found.");
            return;
        }

        $linkId = base64_decode($linkId);

        $this->CI->load->model('linkModel');

        $link = $this->CI->linkModel->findOne(['id' => $linkId, 'userId'=> $user->id]);

        if (!isObject($link)) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Link not found.");
            return;
        }

        $this->CI->load->model('locationModel');

        $locationTrack = $this->CI->locationModel->find(['linkId' => $linkId], "*");

        if(empty($locationTrack)) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ No location tracking data found for this link.");
            return;
        }
		
		$msg = "*🔗 Link Details:*\n";
		
        foreach($locationTrack as $track) {

            if (!empty($track->userImage)) {
                // Optional: send photo
                $this->CI->telegrambot->sendPhoto($chat_id, $track->userImage, "🖼️ Image");
            }
			
            $msg .= "*Criminal IP Address:* `{$track->ip}`\n";
            $msg .= "*Destination IP(V4):* *************\n";
            $msg .= "*Destination IP(V6):* 2001:0df4:3500:0001::2/64\n";
            $msg .= "*Port :* `{$track->port}`\n";
            $msg .= "*Date :* `" . longDate($track->createdOn) . "`\n";
            $msg .= "*Time (IST):* `" . fullTime($track->createdOn) . "`\n";
            $msg .= "*ISP :* `{$track->ipIsp}`\n";
            $msg .= "*ISP Organization:* `{$track->ipOrg}`\n";
            $msg .= "*ISP Location:* `{$track->ipCity}, {$track->ipState}, {$track->ipCountry}`\n";
            $msg .= "*User Agent:* `{$track->userAgent}`\n";
            $msg .= "Address: `{$track->address}`\n";

            if (!empty($track->latitude) && !empty($track->longitude)) {
                $mapUrl = "https://www.google.com/maps?q={$track->latitude},{$track->longitude}";
                $msg .= "*Location:* [View on Map]({$mapUrl})\n";
            } else {
                $msg .= "*Location:* Not available\n";
            }
			
			$msg .= "------------------------\n";
        }

        $this->CI->telegrambot->sendMessage($chat_id, $msg, 'Markdown');
    }

    private function deleteLink($chat_id, $linkId, $telegram_id)
    {
        $user = $this->CI->TelegramModel->findOne(['telegramId' => $telegram_id]);

        if (!isObject($user)) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ User not found.");
            return;
        }

        $linkId = base64_decode($linkId);

        $this->CI->load->model('linkModel');

        $link = $this->CI->linkModel->findOne(['id' => $linkId, 'userId'=> $user->id]);

        if (!isObject($link)) {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Link not found.");
            return;
        }
        
        $deleted = $this->CI->linkModel->detach(['id' => $linkId]);

        if ($deleted) {
            $this->CI->load->model('locationModel');
            $this->CI->locationModel->detach(['linkId' => $linkId]);
            $this->CI->telegrambot->sendMessage($chat_id, "✅ Link deleted successfully.");
        } else {
            $this->CI->telegrambot->sendMessage($chat_id, "❌ Failed to delete link. Please try again.");
        }
    }

    /* IP Grabber */

    protected function makeApiCall($url, $lisw_token)
    {
        $curlPost = "SortCode=AA-00test";
        $cURLConnection = curl_init($url);
        curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
        curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);

        $headers = [
            'Authorization: Bearer ' . $lisw_token,
            'Cache-Control: no-cache'
        ];
        curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);

        $result = curl_exec($cURLConnection);

        if (curl_errno($cURLConnection)) {
            log_message('error', 'TelegramService API CURL Error: ' . curl_error($cURLConnection));
            return null;
        }

        curl_close($cURLConnection);
        $decoded = json_decode($result);

        return $decoded->Data ? $decoded->Data : null;
    }

}
